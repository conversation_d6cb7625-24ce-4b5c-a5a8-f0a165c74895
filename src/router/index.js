import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

/* Router Modules */
// import userCustorm 		from './modules/userCustorm'

export const constantRouterMap = [
  {
    path: '/',
    component: () => import('@/views/home/<USER>'),
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
  },
  {
    path: '/regin',
    component: () => import('@/views/login/regin'),
  },
  {
    path: '/forgetPwd',
    component: () => import('@/views/login/forgetPwd'),
  },
  {
    path: '/download',
    component: () => import('@/views/login/download'),
  },
  {
    path: '/home',
    component: () => import('@/views/home/<USER>'),
  },
  {
    path: '/videoList',
    component: () => import('@/views/home/<USER>'),
  },
  // {
  //   path: '/custorm',
  //   component: () => import('@/views/home/<USER>'),
  // },
  {
    path: '/gameList',
    component: () => import('@/views/game/gameList'),
  },
  {
    path: '/gameCustormList',
    component: () => import('@/views/game/gameCustormList'),
  },
  {
    path: '/playList3',
    component: () => import('@/views/game/playList'),
  },
  {
    path: '/playList2',
    component: () => import('@/views/game/playList2'),
  },
  {
    path: '/playList',
    component: () => import('@/views/game/playList3'),
  },
  {
    path: '/playDetail',
    component: () => import('@/views/game/playDetail'),
  },
  {
    path: '/gd/:code',
    component: () => import('@/views/game/playDetail'),
  },
  {
    path: '/confirmOrder',
    component: () => import('@/views/game/confirmOrder'),
    meta: {
      login: true,
    },
  },
  {
    path: '/store',
    component: () => import('@/views/storeList/index'),
  },
  {
    path: '/confirmOrder2',
    component: () => import('@/views/game/confirmOrder2'),
    meta: {
      login: true,
    },
  },
  {
    path: '/confirmKfOrder',
    component: () => import('@/views/game/confirmKfOrder'),
    meta: {
      login: true,
    },
  },
  // {
  //   path: '/confirmDickerOrder',
  //   component: () => import('@/views/game/confirmDickerOrder'),
  //   meta: {
  //     login: true,
  //   },
  // },
  {
    path: '/introduce',
    component: () => import('@/views/aboutUs/introduce'),
  },

  {
    path: '/payOrder',
    component: () => import('@/views/game/payOrder2'),
    meta: {
      login: true,
    },
  },
  {
    path: '/payOrder2',
    component: () => import('@/views/game/payOrder2'),
    meta: {
      login: true,
    },
  },
  {
    path: '/searchAll',
    component: () => import('@/views/searchAll'),
  },
  // {
  //   path: '/payDickerOrder',
  //   component: () => import('@/views/game/payDickerOrder'),
  //   meta: {
  //     login: true,
  //   },
  // },
  {
    path: '/recyle',
    component: () => import('@/views/recyle/index'),
  },
  {
    path: '/recyleList',
    component: () => import('@/views/recyle/recyleList'),
  },
  // {
  //   path: '/recyleDetail',
  //   component: () => import('@/views/recyle/recyleDetail'),
  // },
  {
    path: '/allSell',
    component: () => import('@/views/account/allSell'),
    meta: {
      login: true,
    },
  },
  {
    path: '/kkquse',
    component: () => import('@/views/kkquse/index'),
  },
  {
    path: '/pushAccount',
    component: () => import('@/views/account/seller/pushAccount'),
  },
  {
    path: '/pushAccount3',
    component: () => import('@/views/account/seller/pushAccount3'),
  },
  {
    path: '/activity',
    component: () => import('@/views/activity/index'),
  },
  {
    path: '/account',
    redirect: '/account/center',
    component: () => import('@/views/account/index'),
    children: [
      {
        path: 'sellDetail',
        component: () => import('@/views/account/sellDetail/index'),
        meta: {
          login: true,
        },
      },
      {
        path: 'questions',
        component: () => import('@/views/account/questions/index'),
        meta: {
          login: true,
        },
      },
      {
        path: 'center',
        component: () => import('@/views/account/home/<USER>'),
        meta: {
          login: true,
        },
      },
      {
        path: 'accountList',
        component: () => import('@/views/account/seller/accountList'),
        meta: {
          login: true,
        },
      },
      {
        path: 'similarList',
        component: () => import('@/views/account/seller/similarList'),
        meta: {
          login: true,
        },
      },
      {
        path: 'sellOrderList',
        component: () => import('@/views/account/seller/sellOrderList'),
        meta: {
          login: true,
        },
      },
      {
        path: 'orderDetail',
        component: () => import('@/views/account/seller/orderDetail'),
        meta: {
          login: true,
        },
      },
      // {
      //   path: 'pushAccount',
      //   component: () => import('@/views/account/seller/pushAccount'),
      // },
      // {
      //   path: 'pushYs',
      //   component: () => import('@/views/account/seller/pushYs'),
      // },
      // {
      //   path: 'pushYjw',
      //   component: () => import('@/views/account/seller/pushYjw'),
      // },
      // {
      //   path: 'pushWz',
      //   component: () => import('@/views/account/seller/pushWz'),
      // },
      {
        path: 'buyerOrder',
        component: () => import('@/views/account/buyer/buyerOrder'),
        meta: {
          login: true,
        },
      },
      {
        path: 'collection',
        component: () => import('@/views/account/buyer/collection'),
        meta: {
          login: true,
        },
      },
      {
        path: 'footerMark',
        component: () => import('@/views/account/buyer/footerMark'),
        meta: {
          login: true,
        },
      },
      {
        path: 'baseNews',
        component: () => import('@/views/account/center/baseNews'),
        meta: {
          login: true,
        },
      },
      {
        path: 'approve',
        component: () => import('@/views/account/center/approve'),
        meta: {
          login: true,
        },
      },
      {
        path: 'identify',
        component: () => import('@/views/account/center/identify'),
        meta: {
          login: true,
        },
      },
      {
        path: 'payIdentify',
        component: () => import('@/views/account/center/payIdentify'),
        meta: {
          login: true,
        },
      },
      {
        path: 'myBargain',
        component: () => import('@/views/account/bargain/myBargain'),
        meta: {
          login: true,
        },
      },
      {
        path: 'supplyBargain',
        component: () => import('@/views/account/bargain/supplyBargain'),
        meta: {
          login: true,
        },
      },{
        path: 'coupon',
        component: () => import('@/views/account/coupon/coupon-list'),
        meta: {
          login: true,
        },
      },
    ],
  },
  {
    path: '/assure',
    component: () => import('@/views/assure/index'),
  },
  {
    path: '/assureList',
    component: () => import('@/views/assure/assureList'),
    meta: {
      login: true,
    },
  },
  {
    path: '/appraisal',
    component: () => import('@/views/appraisal/index'),
  },
  {
    path: '/appraList',
    component: () => import('@/views/appraisal/appraList'),
    meta: {
      login: true,
    },
  },
  {
    path: '/suggest',
    component: () => import('@/views/suggest'),
  },
  // {
  //   path: '/trade',
  //   component: () => import('@/views/trade/index'),
  //   redirect: '/trade/readeKnow',
  //   children: [
  //     {
  //       path: 'readeKnow',
  //       component: () => import('@/views/trade/readeKnow'),
  //     },
  //     {
  //       path: 'bussIntorduce',
  //       component: () => import('@/views/trade/bussIntorduce'),
  //     },
  //     {
  //       path: 'agreement',
  //       component: () => import('@/views/trade/agreement'),
  //     },
  //     {
  //       path: 'expenses',
  //       component: () => import('@/views/trade/expenses'),
  //       meta: {
  //         login: true,
  //       },
  //     },
  //     {
  //       path: 'partaner',
  //       component: () => import('@/views/trade/partaner'),
  //     },
  //     {
  //       path: 'payMent',
  //       component: () => import('@/views/trade/payMent'),
  //     },
  //     {
  //       path: 'agreeUser',
  //       component: () => import('@/views/trade/agreeUser'),
  //     },
  //     {
  //       path: 'private',
  //       component: () => import('@/views/trade/private'),
  //     },
  //     {
  //       path: 'danbao',
  //       component: () => import('@/views/trade/danbao'),
  //     },
  //   ],
  // },
  // {
  //   path: '/aboutUs',
  //   component: () => import('@/views/aboutUs/index'),
  //   redirect: '/aboutUs/company',
  //   children: [
  //     {
  //       path: 'company',
  //       component: () => import('@/views/aboutUs/company'),
  //     },
  //     {
  //       path: 'recruit',
  //       component: () => import('@/views/aboutUs/recruit'),
  //     },
  //     {
  //       path: 'culture',
  //       component: () => import('@/views/aboutUs/culture'),
  //     },
  //   ],
  // },
  {
    path: '/helpCenter',
    component: () => import('@/views/helpCenter/index'),
  },
  {
    path: '/sweepstakes',
    component: () => import('@/views/sweepstakes/index'),
  },
  // {
  //   path: '/notice',
  //   component: () => import('@/views/notice/index'),
  // },
  {
    path: '/tops',
    component: () => import('@/views/pages/tops'),
  },
  {
    path: '/fenghuaTops',
    component: () => import('@/views/pages/fenghuaTops'),
  },
  // {
  //   path: '/form',
  //   component: () => import('@/views/form/index'),
  // },
  // {
  //   path: '/form/done',
  //   component: () => import('@/views/form/done/index'),
  // },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/authredirect'),
  },
  {
    path: '/404',
    component: () => import('@/views/404'),
  },
  { path: '*', redirect: '/' },
];

// function getRoutesList(routes, pre) {
//   return routes.reduce((array, Router) => {
//     const path = `${pre}${Router.path}`;

//     if (Router.path !== '*') {
//       array.push(path);
//     }

//     if (Router.children) {
//       array.push(...getRoutesList(Router.children, `${path}/`));
//     }

//     return array;
//   }, []);
// }

// // getRoutesList(router.options.routes, 'https://zigamiklic.com');
// function getRoutesXML() {
//   const list = getRoutesList(Router.options.routes, 'https://www.gjdbyy.com/')
//     .map(Router => `<url><loc>${Router}</loc></url>`)
//     .join('\r\n');
//   return `<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9 http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">
//     ${list}
//   </urlset>`;
// }

// console.log(getRoutesXML())

export default new Router({
  mode: 'history', //后端支持可开
  // base: 'admin',
  scrollBehavior: () => ({
    y: 0,
  }),
  routes: constantRouterMap,
});
