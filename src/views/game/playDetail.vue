<template>
  <div
    v-loading.fullscreen.lock="loading"
    ref="bodyScroll"
    class="dark_container scrollPageSmoth"
    element-loading-text="正在加载.."
    element-loading-background="rgba(236, 236, 236, 0.40)"
    style="position: relative; background: #fdf5ed"
    @scroll="handleScroll"
  >
    <div style="position: relative">
      <!-- element-loading-spinner="el-icon-loading" -->
      <headerKk :active-index="index" />

      <div
        :style="`background: linear-gradient( ${background} 0%, rgb(255, 255, 255, 0) 100%);`"
        class="paTopCom"
      >
        <div class="safe_width">
          <el-breadcrumb
            separator-class="el-icon-arrow-right"
            class="pdTopBottom my-bread"
            style="padding: 20px 0px"
          >
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/gameList' }"
              >全部游戏</el-breadcrumb-item
            >
            <el-breadcrumb-item
              :to="{
                path: `/playList?productCategoryId=${shopDetailJson.productCategoryId}`,
              }"
              >{{ shopDetailJson.productCategoryName }}</el-breadcrumb-item
            >
            <el-breadcrumb-item class="el-breadcrumb__inner_text">{{
              shopDetailJson.productSn
            }}</el-breadcrumb-item>
          </el-breadcrumb>
          <div class="playDetail_page_header_box">
            <div class="playDetail_page_header_imgText_box">
              <div
                class="page_comStyle goodsDt_wrap spaceBetween"
                style="padding: 0px"
              >
                <div class="goodsDt_pic">
                  <img
                    :src="shopDetailJson.pic"
                    style="width: 100%; min-height: 100%"
                  />
                  <div v-if="shopDetailJson.stock == 0" class="soled_picDetail">
                    <img
                      class="soled_picDetailPic"
                      src="../../../static/soledMid.jpg"
                    />
                  </div>
                </div>
                <div class="goodsDt_right">
                  <el-tooltip
                    :visible-arrow="false"
                    popper-class="tooltip_list2"
                    class="item"
                    effect="dark"
                    placement="bottom"
                  >
                    <div slot="content">
                      <div
                        class="topTips_tit"
                        style="
                          color: #000;
                          font-family: 'PingFang SC';
                          font-size: 17.14px;
                          font-style: normal;
                          font-weight: 500;
                          line-height: normal;
                          line-height: 0px;
                        "
                      >
                        商品详情
                      </div>
                      <div
                      v-if="shopDetailJson.productCategoryId == 75"
                        class="topTips_con light"
                        style="
                          color: rgba(0, 0, 0, 0.6);
                          font-family: 'PingFang SC';
                          font-size: 14px;
                          font-style: normal;
                          font-weight: 400;
                          max-height: 400px;
                          overflow-y: auto;
                          margin-top: 10px;
                        "
                        v-html="
                         textTitleAll
                        "
                      ></div>
                      <div
                      v-else
                        class="topTips_con light"
                        style="
                          color: rgba(0, 0, 0, 0.6);
                          font-family: 'PingFang SC';
                          font-size: 14px;
                          font-style: normal;
                          font-weight: 400;
                          max-height: 400px;
                          overflow-y: auto;
                          margin-top: 10px;
                        "
                        v-html="
                          tedianFilter(shopDetailJson.subTitle, shopDetailJson)
                        "
                      ></div>
                    </div>
                    <div v-if="shopDetailJson.productCategoryId == 75" class="text_linThree fontFamilg goodsDt_title">
                      <!-- {{ shopDetailJson.subTitle | tedianFilter }} -->
                        {{textTitleAll}}
                    </div>
                    <div v-else class="text_linThree fontFamilg goodsDt_title">
                      {{ shopDetailJson.subTitle | tedianFilter }}
                    </div>
                  </el-tooltip>
                  <!-- stock 0已成交 1已预定 publishStatus!=1 未上架 -->
                  <div
                    :class="
                      shopDetailJson.stock == 0 ||
                      shopDetailJson.stock == 1 ||
                      shopDetailJson.publishStatus != 1
                        ? 'goodsDt_right_goodsDt_price2'
                        : 'goodsDt_right_goodsDt_price'
                    "
                  >
                    <!-- class="goodsDt_right_goodsDt_price" -->
                    <div
                      v-if="
                        shopDetailJson.stock == 0 ||
                        shopDetailJson.stock == 1 ||
                        shopDetailJson.publishStatus != 1
                      "
                      :class="
                        topAttrs && topAttrs.length > 0
                          ? 'goodsDt_right_text_ab1'
                          : 'goodsDt_right_text_ab2'
                      "
                      class="goodsDt_right_text_ab"
                    >
                      <div v-if="shopDetailJson.stock == 0">已成交</div>
                      <div v-if="shopDetailJson.stock == 1">已预订</div>
                      <div v-if="shopDetailJson.publishStatus != 1">未上架</div>
                    </div>
                    <img
                      v-else
                      :class="
                        topAttrs && topAttrs.length > 0
                          ? 'goodsDt_right_bk_img'
                          : 'goodsDt_right_bk_img2'
                      "
                      src="../../../static/imgs/logo_Bk.svg"
                      alt=""
                    />

                    <div
                      :style="{
                        'margin-top':
                          topAttrs && topAttrs.length > 0 ? '0px' : '47px',
                      }"
                      style="position: relative; z-index: 9"
                      class="goodsDt_price"
                    >
                      <div class="goodsDt_new_price">价格</div>
                      <div
                        v-if="shopDetailJson.price"
                        class="goodsDt_new_priceNum"
                      >
                        <span>¥ </span>{{ shopDetailJson.price }}
                      </div>
                      <div v-else class="goodsDt_priceNum">联系客服</div>
                    </div>
                    <div
                      v-if="topAttrs && topAttrs.length > 0"
                      class="attr_box"
                    >
                      <div
                        v-for="(item, index) in filteredTopAttrs"
                        :key="index"
                        :class="
                          filteredTopAttrs && filteredTopAttrs.length > 4
                            ? 'attr_one'
                            : 'attr_one1'
                        "
                      >
                        {{ item.label }}
                        <span class="attr_value">{{ item.value }}</span>
                        <span
                          v-if="item.label == '商品编号'"
                          class="iconfontnew icon-fuzhi copy"
                          @click="copyVal(item.value)"
                        ></span>
                      </div>

                      <!-- <div class="attr_line spaceBetween">
                <div class="attr_one">
                  商品编号:<span class="attr_value">{{
                    shopDetailJson.productSn
                  }}</span>
                </div>
                <div class="attr_one">
                  游戏区服:<span class="attr_value">{{
                    shopDetailJson.gameAccountQufu
                  }}</span>
                </div>
              </div>
              <div class="attr_line spaceBetween">
                <div
                  v-for="(item, index) in topAttrs"
                  :key="index"
                  class="attr_one"
                >
                  {{ item.name }}:
                  <span class="attr_value">{{ item.value }}</span>
                </div>
              </div> -->
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="page_comStyle goodsDt_wrap spaceBetween"
                style="padding: 0px; padding-top: 60px"
              >
                <div class="spaceStart goodsDt_collect">
                  <div class="spaceStart" @click="collectAccount">
                    <i
                      v-if="shopDetailJson.is_collect != 1"
                      class="el-icon-star-off collectIcon"
                    ></i>
                    <i v-else class="el-icon-star-on collectIcon active"></i>
                    <div>
                      {{ shopDetailJson.gameSysinfoCollectcount }}人已收藏
                    </div>
                    <el-popover
                      style="margin-left: 5.48px"
                      placement="top-start"
                      width="250"
                      trigger="hover"
                      content="收藏后可第一时间收到信息变更通知"
                    >
                      <span slot="reference" style="cursor: pointer">
                        <IconFont :size="17" icon="cautionary" />
                      </span>
                    </el-popover>
                  </div>
                  <div class="spaceStart goodsDt_collect" @click="showShareFun">
                    <IconFont
                      :size="18.261"
                      icon="share"
                      style="
                        margin-right: 4.87px;
                        margin-left: 17.48px;
                        margin-top: 1px;
                      "
                    />
                    <div>分享</div>
                  </div>
                  <!-- <div class="spaceStart goodsDt_collect" @click="similarFun">
                    <IconFont
                      :size="29"
                      icon="xiangsitu"
                      style="
                        margin-left: 10px;
                        margin-top: 1px;
                        color: red;
                      "
                    />
                    <div>相似成交</div>
                  </div> -->
                  <div class="spaceStart mid_chennuo">
                    <div class="spaceStart mid_chennuo_item">
                      <img src="../../../static/imgs/goods_detail_icon1.svg" />

                      <div>找回包赔</div>
                    </div>
                    <div class="spaceStart mid_chennuo_item">
                      <!-- <img src="../../../static/d2.png" /> -->
                      <img src="../../../static/imgs/goods_detail_icon2.svg" />
                      <div>合同保障</div>
                    </div>
                    <div ref="buyDiv" class="spaceStart mid_chennuo_item">
                      <!-- <img src="../../../static/d3.png" /> -->
                      <img src="../../../static/imgs/goods_detail_icon3.svg" />
                      <div>百人团队</div>
                    </div>
                  </div>
                </div>

                <div class="spaceStart">
                  <a class="spaceCenter customer_service" @click="popupCustom">
                    <!-- <i class="el-icon-headset"></i> -->
                    <span>咨询客服</span>
                  </a>
                  <div v-if="shopDetailJson.stock !== -1" class="spaceStart">
                    <div
                      v-if="canBuy && shopDetailJson.gameGoodsYijia === 1"
                      class="spaceCenter price_reduction"
                      @click="cutDailog"
                    >
                      <!-- <i class="el-icon-collection"></i> -->
                      <div>议价购买</div>
                    </div>
                    <div
                      v-if="shopDetailJson.stock == 0"
                      class="spaceCenter stockDisabled"
                    >
                      <!-- <i class="el-icon-shopping-cart-full"></i> -->
                      <div>已成交</div>
                    </div>

                    <div
                      v-else-if="shopDetailJson.stock == 1"
                      class="spaceCenter stockDisabled"
                    >
                      <!-- <i class="el-icon-shopping-cart-full"></i> -->
                      <div>已预订</div>
                    </div>

                    <div
                      v-else-if="shopDetailJson.publishStatus != 1"
                      class="spaceCenter stockDisabled"
                    >
                      <!-- <i class="el-icon-shopping-cart-full"></i> -->
                      <div>未上架</div>
                    </div>

                    <div
                      v-else-if="ifBuyBtn"
                      class="spaceCenter buyNow"
                      @click="confirnOrder"
                    >
                      <!-- <i class="el-icon-shopping-cart-full"></i> -->
                      立即购买
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 商品描述 -->
      <div class="safe_width">
        <div class="spaceStart">
          <div class="goodsDt_htmlCon fontFamilg" style="padding-top: 20px">
            <div class="detailAcc_warp">
              <div class="detailAcc_title spaceStart">
                <div>账号详情</div>
                <!-- <el-popover placement="right" width="280" trigger="hover">
                <div>
                  <div class="spaceStart" style="padding-bottom: 10px">
                    <img
                      class="tedian_desction_pic"
                      src="../../../static/push/price.png"
                    />
                    <div class="tedian_desction_text one">代表高价值道具</div>
                  </div>
                  <div class="spaceStart" style="padding-bottom: 10px">
                    <img
                      class="tedian_desction_pic"
                      src="../../../static/push/he.png"
                    />
                    <div class="tedian_desction_text two">代表核心道具</div>
                  </div>
                  <div class="spaceStart">
                    <img
                      class="tedian_desction_pic"
                      src="../../../static/push/jue.png"
                    />
                    <div class="tedian_desction_text three">代表绝版道具</div>
                  </div>
                </div>
                <span slot="reference" style="cursor: pointer">
                  <img
                    style="margin-top: -4px; margin-left: 10px"
                    src="../../../static/push/q.jpg"
                  />
                </span>
              </el-popover> -->
              </div>

              <el-table
                v-if="area2List && area2List.length"
                :data="area2List"
                border
                class="border_top_table el-table-border-style"
                style="width: 100%; border-radius: 24px 24px 0px 0px"
              >
                <el-table-column prop="name" label="姓名" width="170">
                  <template slot-scope="scope">
                    {{ scope.row.label }}
                  </template>
                </el-table-column>
                <el-table-column prop="value" label="地址">
                  <template slot-scope="scope">
                    <div class="tableValue" v-html="scope.row.value"></div>
                  </template>
                </el-table-column>
              </el-table>
              <el-table
                v-else
                :data="shopDetailJson.detail_options"
                border
                class="border_top_table el-table-border-style"
                style="width: 100%; border-radius: 24px 24px 0px 0px"
              >
                <el-table-column prop="name" label="姓名" width="170">
                  <template slot-scope="scope">
                    {{ scope.row.name }}
                  </template>
                </el-table-column>
                <el-table-column prop="value" label="地址">
                  <template slot-scope="scope">
                    <div class="tableValue" v-html="scope.row.value"></div>
                  </template>
                </el-table-column>
              </el-table>

              <el-table
                :data="zhsmList"
                border
                class="border_table el-table-border-style"
                style="border-top: 0; border-radius: 0px 0px 24px 24px"
              >
                <el-table-column prop="name" label="姓名" width="170">
                  <template slot-scope="scope"> {{ scope.row.name }} </template>
                </el-table-column>
                <el-table-column prop="value" label="地址">
                  <template slot-scope="scope">
                    <div style="position: relative; padding: 0px">
                      <pre
                        ref="desc"
                        :class="getLineThreeClazz"
                        class="tableValue"
                        @click="toggleLineThree"
                        >{{ scope.row.value }}</pre
                      >
                      <i
                        v-show="showToggleDesc"
                        :class="
                          lineThree
                            ? 'el-icon-arrow-down arrow'
                            : 'el-icon-arrow-up arrow'
                        "
                        @click="toggleLineThree"
                      ></i>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <!-- <div v-if="description" class="spaceStart alignItemsStart">
              <div class="spaceStart descBox">
                <pre
                  ref="desc"
                  @click="toggleLineThree"
                  :class="getLineThreeClazz"
                  >{{ description }}</pre
                >
                <i
                  v-show="showToggleDesc"
                  @click="toggleLineThree"
                  :class="
                    lineThree
                      ? 'el-icon-arrow-up arrow'
                      : 'el-icon-arrow-down arrow'
                  "
                ></i>
              </div>
            </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style="margin-top: 20px">
      <div class="safe_width">
        <div ref="goodsDt_htmlWrap" class="goodsDt_htmlWrap">
          <!-- <div class="goodsDt_htmlType spaceStart">
            <div
              :class="bottomIndex == 0 ? 'active' : ''"
              :key="index"
              class="goodsDt_htmlItem"
              @click="chooseBottom(0)"
            >
              图片详情
            </div>
            <div
              :class="bottomIndex == 888 ? 'active' : ''"
              class="goodsDt_htmlItem"
              @click="chooseBottom(888)"
            >
              交易须知
            </div>
            <div
              v-if="teams && teams.name"
              :class="bottomIndex == 999 ? 'active' : ''"
              class="goodsDt_htmlItem"
              @click="chooseBottom(999)"
            >
              交易群
            </div>
          </div> -->
          <!-- border-class="tab-border" -->
          <!-- <gameList> </gameList> -->
          <myTab
            :class="`tab_playDetail${tabType}`"
            :tabs="tabData"
            class="playDetail_tab_btn"
            @click="changeMyTab"
          >
            <template>
              <div class="goodsDt_htmlCon fontFamilg">
                <div
                  v-if="
                    albumPicsByType &&
                    albumPicsByType.length &&
                    bottomIndex == 0
                  "
                  class="spaceStart ntypeBox"
                   ref="albumClickBox"
                >
                  <div
                    v-for="(item, index) in albumPicsByType"
                    :class="getNClazz(item)"
                    :key="index"
                    @click="changeNtype(item,index)"
                   
                  >
                    {{ item.type }}
                  </div>
                </div>
                
                <div v-if="bottomIndex == 0">
                  <!-- 王者 -->
                <div v-if="!!this.wzryId">
                  <el-tabs v-model="activeName">
                    <el-tab-pane label="皮肤" name="skin"></el-tab-pane>
                    <el-tab-pane label="英雄" name="hero"></el-tab-pane>
                  </el-tabs>
                  <div v-if="activeName == 'skin'">
                    <div class="spaceStart">
                      <div
                        :class="activeType == 'career' ? 'active' : ''"
                        class="type_btn"
                        @click="changeActiveType('career')"
                      >
                        职业
                      </div>
                      <div
                        :class="activeType == 'rare' ? 'active' : ''"
                        class="type_btn"
                        @click="changeActiveType('rare')"
                      >
                        稀有度
                      </div>
                    </div>
                    <div
                      v-if="activeType == 'career'"
                      class="spaceStart wztype"
                    >
                      <div
                        v-for="(item, index) in wzList"
                        :class="item === wztype ? 'active' : ''"
                        :key="index"
                        class="item"
                        @click="changeWztype(item)"
                      >
                        {{ item }}
                      </div>
                    </div>
                  </div>
                </div>
                  <!-- 王者荣耀有营地 id 的 -->
                  <div
                    v-if="productCategoryId == 82 && wzryId"
                    style="padding-bottom: 60px"
                  >
                    <div v-if="activeName == 'skin'">
                      <div v-if="activeType == 'career'">
                        <div
                          v-for="(item, index) in arrDtPicForShow2"
                          :key="index"
                          class="spaceStart skin_box"
                        >
                          <div class="hero">
                            <div class="imgbox">
                              <img :src="item.heroIcon" class="img" />
                            </div>
                            <div class="name">{{ item.heroName }}</div>
                            <div class="namenum">
                              {{ item.list.length }}/{{ item.skinNum }}
                            </div>
                          </div>
                          <div
                            v-for="(ele, idx) in item.list"
                            :key="idx"
                            class="skin spaceStart"
                          >
                            <img :src="ele.skinImg" class="skinimg" />
                            <div class="skinname">{{ ele.skinName }}</div>
                          </div>
                        </div>
                      </div>
                      <div v-else class="skin_box2">
                        <div
                          v-for="(item, index) in arrDtPicForShow2"
                          :key="index"
                        >
                          <div class="name">
                            {{ item.name }} {{ item.list.length }}
                          </div>
                          <div class="spaceStart skins">
                            <div
                              v-for="(ele, idx) in item.list"
                              :key="idx"
                              class="skin_warp"
                            >
                              <img :src="ele.skinImg" class="skin" />
                              <div class="skinname">{{ ele.skinName }}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else class="hero_box">
                      <div
                        v-for="(item, index) in arrDtPicForShow3"
                        :key="index"
                      >
                        <div class="name">
                          {{ item.name }} {{ item.list.length }}/{{
                            index == 0 ? '11' : '110'
                          }}
                        </div>
                        <div class="spaceStart heros">
                          <div
                            v-for="(ele, idx) in item.list"
                            :key="idx"
                            class="hero_warp"
                          >
                            <img :src="ele.heroIcon" class="hero" />
                            <div class="hero_name">{{ ele.name }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    v-else
                    class="box_one"
                    style="position: relative; line-height: 0px"
                  >
                    <div
                      ref="arrDtPicForShowLine"
                      style="position: absolute; top: -70px"
                    ></div>
                    <!--:preview-src-list="arrDtPicForShow" -->
                    <el-image
                      v-for="(item, index) in arrDtPicForShow"
                      :key="index"
                      :src="item"
                      lazy
                      class="playDeatil_image"
                      style="
                        width: 100%;
                        height: 100%;
                        line-height: 0px;
                        margin-top: 12px;
                        border-radius: 12px;
                      "
                      fit="cover"
                      @click="imgPreviewSrc(index)"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="bottomIndex == 888"
                  class="introduce_box"
                  v-html="introduceHtml"
                ></div>
                <div
                  v-if="bottomIndex == 999 && qqlist != []"
                  class="qqList_box"
                >
                  <div
                    v-for="(item, index) in qqlist"
                    :key="index"
                    class="goodsDt_qqGroup"
                  >
                    <!-- <i
                      class="el-icon-s-custom"
                      style="font-size: 1.7rem; color: #ef7a1e"
                    ></i> -->
                    <img
                      class="qqGroup_logo"
                      src="../../../static/imgs/logo_Bk.svg"
                      alt=""
                    />
                    <div class="qqGroup_box_rigth">
                      <div class="title">{{ teams.name }}</div>
                      <div class="qqNum">QQ:{{ item.code }}</div>
                      <div
                        class="goodsDt_qqGroup_niceButton"
                        @click="addGroupFun(item)"
                      >
                        点我加群
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  v-if="bottomIndex == 9999"
                  class="qqList_box"
                >
                <div style="width: 100%;    margin-top: -10px;">
                  <!--  -->
                 <div v-if="consultationRecordsList.length>0||(description&&description.length<50)">
                  <div v-if="description&&description.length<50" style="border-bottom:1px solid #e9e9e9 ;font-family: PingFang Sc;padding-bottom: 5px;margin-bottom: 10px;font-size: 18px;display: flex;align-items: center;justify-content: space-between;">
                    <span>{{this.description}}</span>
                    <span style="color: #9a9a9a;font-size: 16px; font-style: italic;">（卖家说内容为号主历史咨询答复，不作为交易依据）</span>
                  </div>
                    <div  v-for="(item,index) in consultationRecordsList">
                   <div  style="border-bottom:1px solid #e9e9e9 ;font-family: PingFang Sc;padding-bottom: 5px;margin-bottom: 10px;">
                    <div  class="spaceStart">
                      <img style="width: 26px;margin-right:10px;" src="../../../static/imgs/wen.png" alt="">
                      <div style="font-size: 18px;">{{item.consultQuestion}}</div>
                    </div>
                    <div class="spaceStart">
                      <img style="width: 26px;margin-right:10px;" src="../../../static/imgs/da.png" alt="">
                      <div style="color: #9a9a9a;font-size: 16px;">{{item.consultContent}}</div>
                    </div>
                   </div>
                  </div>
                 </div>
                  <div style="text-align: center;" v-else>暂无卖家说</div>
                  <!-- <el-collapse class="detail_collapse" v-model="activeNames" @change="handleChange">
  <el-collapse-item v-for="(item,index) in consultationRecordsList" :title="item.consultQuestion" :name="item.id">
    <div>{{item.consultContent}}</div>
  </el-collapse-item>
</el-collapse> -->
                </div>
              </div>
              </div>
            </template>
          </myTab>
        </div>
      </div>
      <div v-if="wxBoxFlag&&!userInfo.isWxPush" :style="{bottom:!isVisible?'95px':'0px'}" class="goWxBox">
        <div class="spaceAlignCenter">
          <IconFont
            :size="50"
            icon="wx-kk-icon"
            style="margin: 0px 16px 0px 0px; height: 50px; width: 50px"
            class="c-primary"
          />
          <div class="goWxBoxContent">
            <div>开启微信并提交</div>
            <div class="goWxBoxContent-text">
              平台信息及时知，重要信息不错过
            </div>
          </div>
        </div>
        <div class="spaceAlignCenter right-btn-text" @click="goWechat">
          <span>去开启</span>
          <span style="font-size: 26px;margin-left: 5px;margin-top: 1px">&#155</span>
        </div>
        <div class="goWxBoxClone" @click="wxBoxClone">
              <IconFont
              :size="16"
              icon="close"
              class="c-primary"
              style="margin: 0px;color: #fff;"
              color="#ffffff"
            />
        </div>
      </div>
      <div
        :class="{ 'is-visible': !isVisible }"
        :style="bottomStyle"
        class="safe_width toggle-div"
        style="backdrop-filter: blur(20px)"
      >
        <div class="footerStickyBox goodsDt_wrap spaceBetween">
          <div class="spaceStart">
            <div class="spaceStart goodsDt_collect" @click="collectAccount">
              <i
                v-if="shopDetailJson.is_collect != 1"
                class="el-icon-star-off collectIcon"
              ></i>
              <i v-else class="el-icon-star-on collectIcon active"></i>
              <div>{{ shopDetailJson.gameSysinfoCollectcount }}人已收藏</div>
              <el-popover
                style="margin-left: 5.36px"
                placement="top-start"
                width="250"
                trigger="hover"
                content="收藏后可第一时间收到信息变更通知"
              >
                <span slot="reference" style="cursor: pointer">
                  <IconFont :size="17" icon="cautionary" />
                </span>
              </el-popover>
            </div>
            <div class="spaceStart goodsDt_collect" @click="showShareFun">
              <IconFont
                :size="18.261"
                icon="share"
                style="
                  margin-right: 4.87px;
                  margin-left: 17.48px;
                  margin-top: 1px;
                "
              />
              <div>分享</div>
            </div>
            <div class="spaceStart mid_chennuo" style="margin-left: 59.13px">
              <div class="spaceStart mid_chennuo_item">
                <!-- <img src="../../../static/d1.png" /> -->
                <img src="../../../static/imgs/goods_detail_icon1.svg" />
                <div>找回包赔</div>
              </div>
              <div class="spaceStart mid_chennuo_item">
                <!-- <img src="../../../static/d2.png" /> -->
                <img src="../../../static/imgs/goods_detail_icon2.svg" />
                <div>合同保障</div>
              </div>
              <div class="spaceStart mid_chennuo_item">
                <!-- <img src="../../../static/d3.png" /> -->
                <img src="../../../static/imgs/goods_detail_icon3.svg" />
                <div>百人团队</div>
              </div>
            </div>
          </div>

          <div class="spaceStart">
            <a class="spaceCenter customer_service" @click="popupCustom">
              <!-- <i class="el-icon-headset"></i> -->
              <span>咨询客服</span>
            </a>
            <div v-if="shopDetailJson.stock !== -1" class="spaceStart">
              <div
                v-if="canBuy && shopDetailJson.gameGoodsYijia === 1"
                class="spaceCenter price_reduction"
                @click="cutDailog"
              >
                <!-- <i class="el-icon-collection"></i> -->
                <div>议价购买</div>
              </div>
              <div
                v-if="shopDetailJson.stock == 0"
                class="spaceCenter stockDisabled"
              >
                <div>已成交</div>
              </div>
              <div
                v-else-if="shopDetailJson.stock == 1"
                class="spaceCenter stockDisabled"
              >
                <div>已预订</div>
              </div>
              <div
                v-else-if="shopDetailJson.publishStatus != 1"
                class="spaceCenter stockDisabled"
              >
                <div>未上架</div>
              </div>
              <div
                v-else-if="ifBuyBtn"
                class="spaceCenter buyNow"
                @click="confirnOrder"
              >
                <!-- <i class="el-icon-shopping-cart-full"></i> -->
                立即购买
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 认证 -->
      <el-dialog
        :visible.sync="dialogVisibleInden"
        width="30%"
        center
        title="实名认证"
      >
        <div class="iden_text">
          尊敬的用户您好，根据相关法律法规要求，需实名认证方可进行购买。
        </div>
        <div class="spaceCenter">
          <div
            class="spaceCenter plDt_btn"
            style="margin-bottom: 20px; width: 90%"
            @click="indentifyGo"
          >
            去实名
          </div>
        </div>
      </el-dialog>

      <!-- 加微信群 -->
      <el-dialog
        :visible.sync="dialogVisibleWxCode"
        width="30%"
        center
        title="扫码加群"
      >
        <div>
          <img :src="wxUrlCode" style="width: 100%" />
        </div>
      </el-dialog>

      <!-- 微信联系 -->
      <el-dialog
        :visible.sync="dialogVisibleDaishou"
        width="30%"
        center
        title="添加客服微信咨询"
      >
        <div class="code_wrap">
          <canvas
            id="QRCode_header_kfwx"
            style="width: 150px; height: 150px"
          ></canvas>
        </div>
        <div style="text-align: center; padding-top: 20px">
          扫码添加客服进行咨询，微信号：{{ kfObj['微信号'] }}
        </div>
      </el-dialog>

      <!-- 分享 -->
      <div v-show="showShare" class="mask_container">
        <div
          class="msk_body"
          style="padding: 30px 31px 28px 32px; position: relative"
        >
          <img
            src="../../../static/imgs/dialog_clone.png"
            class="msk_body_cloneBtn"
            style="
              position: absolute;
              width: 54px;
              height: 54px;
              right: -86px;
              top: -27px;
              cursor: pointer;
            "
            @click="cloaseShare"
          />
          <img
            class="msk_body_logo_bk"
            src="../../../static/imgs/playDetail_share_logo.png"
            alt=""
          />
          <div style="position: relative">
            <img
              style="width: 143px; height: 41.921px"
              src="../../../static/imgs/text_Logo.svg"
              alt=""
            />
          </div>
          <div class="game_Spic_box">
            <div class="game_Spic">
              <img :src="shopDetailJson.pic" />
            </div>
            <div class="game_Spic_share_title_box">
              <div class="title">
                <!-- <span class="gameSn">【{{ shopDetailJson.productSn }}】</span> -->
                【{{ shopDetailJson.productSn }}】
                <span
                  v-if="shopDetailJson && shopDetailJson.productCategoryName"
                  style="margin-left: -35px"
                  >【{{ shopDetailJson.productCategoryName }}】</span
                >
              </div>
              <div class="share_attr_box">
                <div
                  v-for="(item, index) in topAttrs"
                  :key="index"
                  :style="{ 'width': item.label == '商品编号' ? '100%' : '' }"
                  class="share_attr_one"
                >
                  {{ item.label }}:
                  <span class="attr_value">{{ item.value }}</span>
                  <span
                    v-if="item.label == '商品编号'"
                    class="iconfontnew icon-fuzhi copy"
                    @click="copyVal(item.value)"
                  ></span>
                </div>
              </div>
            </div>
          </div>
          <div class="game_share_descriptive">
            <div class="title">账号特点与描述</div>
            <div class="description text_linTwo">
              {{ shopDetailJson.description }}
            </div>
          </div>
          <div class="game_share_price spaceBetweenNoAi">
            <div class="game_share_price_box">
              <div class="price_title">售价</div>
              <div class="price">¥&nbsp;{{ shopDetailJson.price }}</div>
            </div>
            <div class="game_share_QrCode">
              <canvas
                id="QRCode_header_play"
                style="width: 108px; height: 108px"
              ></canvas>
            </div>
          </div>
          <!-- <div class="game_Spic">
            <img :src="shopDetailJson.pic" />
          </div>
          <div class="m_tit_main spaceCenter">
            <img
              class="smallIcon_acc"
              style="margin-right: 6px; width: 22px"
              src="../../../static/account_list_icon_name.png"
            />
            <div>{{ shopDetailJson.productSn }}</div>
          </div>
          <div class="m_tit_main spaceCenter">
            <img
              class="smallIcon_acc"
              style="margin-right: 10px; width: 18px"
              src="../../../static/account_list_icon_server.png"
            />
            <div>{{ shopDetailJson.gameAccountQufu }}</div>
          </div>
          <div class="m_tit_sub">{{ shopDetailJson.description }}</div>
          <div class="share_priceBox">
            <div class="spaceCenter share_price_mid">
              <div style="font-size: 14px; padding-right: 8px">价格</div>
              <div>{{ shopDetailJson.price }}</div>
            </div>
          </div>
          <div class="code_wrap">
            <canvas
              id="QRCode_header_play"
              style="width: 150px; height: 150px"
            ></canvas>
          </div>
          <div class="tit_two">扫一扫 查看详情</div> -->
        </div>
      </div>
      <div v-if="showShare2" class="mask_container" @click="cloaseShare">
        <share :product-id="productId"></share>
      </div>

      <safeNews />
      <div class="footerCt">
        <footerKk />
      </div>
      <navigation-fixed :flag-id="productCategoryId" @goPageTop="backTopPage" />
      <!-- <div class="playDetailPreviewImg"></div> -->
    </div>
    <!-- <imagePriview
      v-if="showViewer"
      :z-index="10000"
      :initial-index="imgViewer"
      :on-close="closeViewer"
      :url-list="arrDtPicForShow"
    /> -->
    <swperImagePriview
    v-if="showViewer"
      :z-index="10000"
      :initial-index="imgViewer"
      :on-close="closeViewer"
      :url-list="arrDtPicForShow"
    />
    <bargainDialog
      :visible="dialogVisible"
      :dialog-width="'585px'"
      class="bargainDialog"
      @dialogClone="dialogClone"
    >
      <template slot="right_title">
        <span class="bargain_right_title">砍价购买</span>
      </template>
      <template slot="content">
        <div class="spaceBetween bargain_img_box">
          <div class="cutDt_pic">
            <el-image
              :src="shopDetailJson.pic"
              style="width: 100%; height: 100%"
              fit="cover"
            ></el-image>
          </div>
          <div
            :class="
              offerPrice && offerPrice != 0 ? 'cutDt_body1' : 'cutDt_body'
            "
          >
            <div class="text_linTwo cutDt_body_name">
              {{ shopDetailJson.subTitle | tedianFilter }}
            </div>
            <div class="dialog_account_price">
              <span>商品价格：</span><span style="color: #ff720c;">￥{{ shopDetailJson.price }}</span>
            </div>
            <div class="cutDt_price">
              <span
                style="
                  font-size: 14px;
                  color: #1b1b1b;
                  font-weight: 400;
                  font-family: PingFang SC;
                "
                >当前最高出价：</span
              >
              <span v-if="maxPrice && maxPrice != 0">¥{{ maxPrice }}</span>
              <span v-else>暂无议价</span>
            </div>
            <div v-if="offerPrice && offerPrice != 0" class="cutDt_price">
              <span
                style="
                  font-size: 14px;
                  color: #1b1b1b;
                  font-weight: 400;
                  font-family: PingFang SC;
                "
                >历史最高出价：</span
              >
              <span v-if="offerPrice && offerPrice != 0"
                >¥{{ offerPrice }}</span
              >
            </div>
          </div>
        </div>

        <div class="cut_cnver">意向金注意事项</div>
        <div
          v-if="productCategoryId == 2 || productCategoryId == 1"
          class="cut_conHtml"
        >
          1、报价金额不能低于商品价格的50% <br />
          2、重新报价需取消原报价 <br />
          3、砍价需支付出价金额的5% (最高500) 的意向金 <br />
          4、议价成功24小时内未能成功支付号款，议价金将全额赔付给卖家
        </div>
        <div v-else class="cut_conHtml">
          1、报价金额不能低于商品价格的70% <br />
          2、重新报价需取消原报价 <br />
          3、砍价需支付出价金额的5% (最高2000) 的意向金<br />
          4、议价成功24小时内未能成功支付号款，议价金将全额赔付给卖家
        </div>
        <div class="spaceStartNotAi cut_checkout_box">
          <!-- <el-checkbox
            v-model="checked"
            style="margin-right: 6px"
          ></el-checkbox> -->
          <IconFont
            v-if="!checked"
            :size="17.14"
            style="margin-right: 6px; cursor: pointer; margin-top: -0px"
            icon="unchecked"
            @click="changChecked(true)"
          />
          <img
            v-if="checked"
            style="width: 17.14px; margin-right: 6px; cursor: pointer"
            src="../../../static/imgs/confirmOrder_order_chebox_icon.svg"
            alt=""
            @click="changChecked(false)"
          />
          <div>
            我已阅读并同意<router-link
              class="cut_checkout_box_text"
              to="/helpCenter?id=99"
              >购买须知</router-link
            >
          </div>
        </div>
        <!-- price -->
        <div class="spaceBetweenNoAi" style="margin-top: 50.563px">
          <div class="spaceStart cutDt_pushWrap">
            <div class="cutDt_push_btn">报价</div>
            <input
              v-model="price"
              :style="{ 'font-size': price ? '20.56px' : '14px' }"
              type="tel"
              maxlength="9"
              onkeypress="return(/[\d]/.test(String.fromCharCode(event.keyCode)))"
              class="cutDt_push_ipt"
              placeholder="请输入报价"
            />
          </div>
          <div class="spaceCenter cutDt_submit" @click="addBindPrice">砍价</div>
        </div>
        <div style="margin-left: 20px;">请输入<span style="color: #ff720c;">¥{{ Math.ceil(shopDetailJson.price * 0.7) }}～¥{{ shopDetailJson.price }}</span> 之间的报价金额</div>
      </template>
    </bargainDialog>
    <bargainDialogBox @bargainClone="bargainClone" :bargainVisible="bargainVisible" :productId="productId"/>
    <wechatDialog :visible="wechatDialogVisible" @dialogClone="weChatDialogClone">
      <template slot="content">
        <div class="wechatDialog_box">
          <div class="title">扫码关注公众号</div>
          <!-- <img class="qRcode" src="" alt="" /> -->
          <div style="position: relative">
            <canvas
              id="wx_qRcode"
              style="width: 128px; height: 128px; border: 1px solid #ffb74a"
            >
            </canvas>
            <img
              style="
                width: 34.26px;
                height: 34.26px;
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
              "
              src="../../../static/imgs/qrcode_wechat.png"
              alt=""
            />
          </div>
          <div class="divider"></div>
          <div class="footer_title">开启提醒享受以下服务</div>
          <div class="footer_type">
            <div>审核通知</div>
            <div class="divide_right"></div>
            <div>上架通知</div>
            <div class="divide_right"></div>
            <div>交易通知</div>
          </div>
        </div>
      </template>
    </wechatDialog>
    <daojuNumDialog :newly="newly" @dialogClone="daojuDialogClone" :count="daojuCount" :item="daojuDialogObj" :visible="daojuNumVisible"/>
      <!-- <similarDealDialog
      :product-category-id="similarDealDialogId"
      :price="similarDealDialogPrice"
      :visible="similarDealDialogVisible"
      @dialogClone="similarDealDialogClone"
    /> -->
  </div>
</template>

<script>
// import gameList from '@/components/gameList/index';
import share from '@/components/share/share';
import { getWxQrcode } from '@/api';
import _ from 'lodash';
import linkConfig from '../../../config/link.js';
import util from '@/utils/index';
import myTab from '@/components/myTab/index';
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import navigationFixed from '@/components/navigationFixed/index';
import { getHelpDetail } from '@/api/index';
import QRCode from 'qrcode';
import imagePriview from './imagePriview.vue';
import { mapState } from 'vuex';
import isLogin from '@/utils/isLogin';
import bargainDialog from '@/components/borderDialog/index2.vue';
import wechatDialog from '@/components/borderDialog/index2.vue';
import bargainDialogBox from '@/components/borderDialog/bargainDialog.vue';
// import similarDealDialog from '@/components/borderDialog/similarDealDialog.vue';
import daojuNumDialog from '@/components/borderDialog/daojuNumDialog.vue'
import { getProductCategory } from '@/api/search.js';
// import listAll from './1.json'
import { generateKKOrder2 ,productConsultationRecords} from '@/api/confirmOrder.js';
import swperImagePriview from '@/components/imagePriview.vue'
import { getKfList, getMemberHisKFList, m2kfTalk } from '@/api/kf.js';
import { getCertDetail } from '@/api/safeCenter';
import {
  getDetail,
  getDetailByCode,
  productCollectionAdd,
  productCollectionDetele,
  readHistoryCreate,
  topOfferPrice,
  
} from '@/api/playDetail';
import {
  getSkinAndHero,
  getSkinAndHeroUrl
} from '@/api/playWZDetail';
const OPTSOUT = {
  '1': '面板属性',
  '2': '打造内功',
  '3': '天赏外观',
  '4': '普通外观',
  '5': '其他物品',
};
// const OPTSOUTSORT = {
//   '面板属性': 1,
//   '打造内功': 2,
//   '天赏外观': 3,
//   '普通外观': 4,
//   '其他物品': 5,
// };
const WZHERO = [
  '武则天',
  '敖隐',
  '嬴政',
  '娜可露露',
  '不知火舞',
  '橘右京',
  '艾琳',
  '夏洛特',
  '金蝉',
  '赵云',
  '韩信',
];
export default {
  components: {
    share,
    headerKk,
    footerKk,
    safeNews,
    navigationFixed,
    myTab,
    imagePriview,
    bargainDialog,
    wechatDialog,
    daojuNumDialog,
    swperImagePriview,
    bargainDialogBox
    // similarDealDialog,
    // splayDetail,
  },
  data() {
    return {
      bargainVisible:false,
      // similarDealDialogId: null,
      // similarDealDialogPrice: null,
      // similarDealDialogVisible: false,
      consultationRecordsList:[],
      newly:3,
      tabType: 0,
      showViewer: false,
      wxBoxFlag: false,
      wechatDialogVisible: false,
      imgViewer: 0,
      tabData: [{ title: '图片详情' }, { title: '交易须知' }],
      area2List: [],
      zhsmList: [],
      albumPicsTypeOptions: [],
      productAttributeValueList: [],
      bottomStyle: '',
      description: '',
      topAttrs: [],
      collects: [],
      qrcodeImg: '',
      showShare: false,
      showShare2: false,
      dialogVisibleWxCode: false, // 添加微信群弹窗
      wxUrlCode: '', // 微信二维码
      productCategoryId: '',
      bottomIndex: 0,
      picUrl: '',
      productId: '',
      shopDetailJson: {
        yusuan: '0.00',
      },
      arrDtPic: [],
      introduceHtml: '',
      qqlist: [],
      checked: false, // 议价协议
      loading: false,
      index: 1, // 顶部当前选中
      dialogVisible: false,
      dialogVisibleInden: false,
      maxPrice: 0, // 当前最高议价
      offerPrice: 0, //历史最高价
      price: '', // 新出议价
      // txImcode: '',
      teams: {},
      background: '',
      titleText: '', //商品信息标题
      dialogVisibleDaishou: false,
      productCategory: {},
      kfObj: {},
      ntypeActive: '',
      albumPicsByType: [],
      arrDtPicForShow: [],
      arrDtPicForShow2: [],
      arrDtPicForShow3: [],
      activeName: 'skin',
      activeType: 'career',
      wztype: '全部',
      wzList: ['全部', '射手', '法师', '坦克', '刺客', '战士', '辅助'],
      wzData: '',
      wzryId: '',
      wzUrl:'',
      isVisible: true,
      lineThree: true,
      getLineThreeClazz: 'spaceStart preBox',
      showToggleDesc: false,
      saveCustom: {},
      daojuDialogObj:{},
      daojuCount:0,
      daojuNumVisible:false,
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
    canBuy() {
      return (
        this.shopDetailJson.publishStatus == 1 &&
        this.saveCustom.buyType !== 'kefu' &&
        this.shopDetailJson.price &&
        this.shopDetailJson.price != 0 &&
        (this.shopDetailJson.stock === 9||this.shopDetailJson.stock === 8) &&
        this.shopDetailJson.memberId != this.userInfo.id
      );
    },
    ifBuyBtn() {
      return (
        this.shopDetailJson.publishStatus == 1 &&
        this.shopDetailJson.price &&
        this.shopDetailJson.price != 0 &&
        this.shopDetailJson.stock === 9 &&
        this.shopDetailJson.memberId != this.userInfo.id
      );
    },
    filteredTopAttrs() {
      const hasProductCode = this.topAttrs.some(
        (item) => item.label === '商品编号'
      );
      if (hasProductCode) {
        return this.topAttrs.slice(0, 4);
      }
      return this.topAttrs;
    },
    textTitleAll(){
      return this.description ? `【${this.description}】${this.getTdTxt(this.productAttributeValueList)}` : this.getTdTxt(this.productAttributeValueList);
    }
  },
  watch: {
    '$route.fullPath'() {
      this.init();
    },
    productId(v) {
      if (v) {
        this.getCodePic(); // 生成H5二维码链接
      }
    },
  },
  created() {},
  onShow() {},
  mounted() {
    this.setupIntersectionObserver();
    this.init();
    this.wxBoxFlag = isLogin();
  },
  beforeDestroy() {
    if (this.observer) {
      this.observer.disconnect();
    }
  },
  methods: {
    bargainClone(t){
      this.bargainVisible=false
      if(t){
        this.$store.dispatch('ToggleIM', true);
      }
    },
    getTdTxt(item) {
      // if (item.productCategoryId != 75) {
      //   // 诛仙世界
      //   return item.description;
      // } else {
        // 逆水寒等
        const str1 = item
          .filter((ele) =>
            ['稀有外观', '天赏祥瑞', '天赏发型'].includes(ele.productAttributeName),
          )
          .map((ele) => ele.value)
          .join(' ');
        const str2 = item
          .filter(
            (ele) =>
              ['灵韵数量', '天霓染'].includes(ele.productAttributeName) &&
              ele.value &&
              ele.value !== '0',
          )
          .map((ele) => ele.productAttributeName + ele.value)
          .join(' ');
        return item.description?item.description:(str2 + ' ' + str1).replace('数量', '').replace(/,/g, ' ');
      // }
    },
    creatQrCode() {
      var that = this;
      const picUrl = this.picUrl;
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 128, //宽
        height: 128, //高
        text: picUrl, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };

      let msg = document.getElementById('wx_qRcode');
      QRCode.toCanvas(msg, picUrl, opts, function (error) {
        if (error) {
          that.$message.error('二维码加载失败');
        } else {
          const canvas = msg.getContext('2d');
          const logoWidth = 34.56;
          const logoHeight = 34.56;
          const logo = new Image();
          logo.src = '../../../../static/imgs/qrcode_wechat.png';
          logo.onload = function () {
            const x = (canvas.canvas.width - logoWidth) / 2;
            // 计算logo在二维码垂直方向的居中坐标
            const y = (canvas.canvas.height - logoHeight) / 2;
            // 将logo按照固定宽高绘制到二维码中间垂直居中位置
            canvas.drawImage(logo, x, y, logoWidth, logoHeight);
          };
        }
      });
    },
    //关注公众号
    goWechat() {
      getWxQrcode().then((res) => {
        this.wechatDialogVisible = true;
        this.$nextTick(() => {
          this.picUrl = res.data.url;
          this.creatQrCode();
        });
      });
    },
    weChatDialogClone() {
      this.wechatDialogVisible = false;
    },
    wxBoxClone() {
      this.wxBoxFlag = false;
    },
    dialogClone() {
      this.dialogVisible = false;
    },
    changChecked(flag) {
      this.checked = flag;
      if (flag) {
        this.checkedFlag = false;
      }
    },
    handleScroll() {
      const scrollContainer = this.$refs.bodyScroll;
      const scrollHeight = scrollContainer.scrollHeight; // 滚动元素的总高度
      const scrollTop = scrollContainer.scrollTop; // 已滚动的距离
      const clientHeight = scrollContainer.clientHeight; // 滚动元素的可视高度
      const distanceToBottom = scrollHeight - scrollTop - clientHeight;
      this.bottomStyle =
        distanceToBottom <= 320
          ? // ? `bottom:${320 - distanceToBottom}px`
            'bottom:0px'
          : 'bottom:0px';
    },
    // getLineThreeClazz() {
    //   if (this.lineThree) {
    //     return 'spaceStart preBox text_linThree';
    //   } else {
    //     return 'spaceStart preBox';
    //   }
    // },
    toggleLineThree() {
      this.lineThree = !this.lineThree;
      if (this.lineThree) {
        this.getLineThreeClazz = 'spaceStart preBox text_linThree';
      } else {
        this.getLineThreeClazz = 'spaceStart preBox';
      }
    },
    setupIntersectionObserver() {
      const target = this.$refs.buyDiv;

      this.observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          this.isVisible = entry.isIntersecting;
        },
        {
          root: null,
          rootMargin: '0px',
          threshold: [0],
        }
      );
      this.observer.observe(target);
    },
    init() {
      if (this.$route.query.autokanjia == 1) {
        this.cutDailog();
      }
      if (isLogin()) {
        this.$store.dispatch('getUserInfoStore',{isForcible:true});
      }
      if (this.$route.query.productId) {
        this.productId = this.$route.query.productId;
        // if (!_.isNaN(parseInt(this.productId))) {
        //   localStorage.setItem('guessProductId', this.productId);
        // }
        this.initGame();
      } else if (this.$route.params.code) {
        this.productSn = this.$route.params.code;
        this.initGame();
      } else {
        this.$confirm('该链接已失效，请返回首页重新找号', '提示', {
          closeOnClickModal: false,
          confirmButtonText: '返回首页',
          cancelButtonText: '取消',
          type: 'warning',
        })
          .then(() => {
            location.href = 'https://www.kkzhw.com';
          })
          .catch(() => {
            location.href = 'https://www.kkzhw.com';
          });
      }
    },
    changeNtype(item, index,noScroll) {
      console.log(item, noScroll,11111)
      const divElement = this.$refs.goodsDt_htmlWrap;
      const height = divElement.offsetHeight;
      console.log(height, 22222);

      this.ntypeActive = item.type;
      if (this.ntypeActive === '全部图片') {
        this.arrDtPicForShow = this.arrDtPic;
      } else {
        const findIt = this.albumPicsByType.find(
          (ele) => this.ntypeActive == ele.type
        );
        this.arrDtPicForShow = findIt ? findIt.items || [] : [];
      }
      this.moveThumbToCenter(index)
      if (!noScroll) {
        this.$refs.arrDtPicForShowLine.scrollIntoView();
      }
    },
    moveThumbToCenter(targetIndex) {
      this.scale = 1
      const thumbList = this.$refs.albumClickBox;
      const thumbItems = thumbList.querySelectorAll('div');
      const targetThumb = thumbItems[targetIndex];
      const thumbWidth = targetThumb.offsetWidth;
      const containerWidth = thumbList.offsetWidth;
      const targetOffsetLeft = targetThumb.offsetLeft;
      const containerScrollLeft = thumbList.scrollLeft;
      const centerOffset = containerWidth / 2 - thumbWidth / 2;

      // 计算需要滚动的距离
      const scrollTo = targetOffsetLeft - centerOffset;

      // 使用平滑滚动
      thumbList.scrollTo({
        left: scrollTo,
        behavior: 'smooth'
      });

    },
    // albumClickBox
    getNClazz(item) {
      return this.ntypeActive == item.type ? `ntype  active` : `ntype`;
    },
    groupPics() {
      const product = this.shopDetailJson;
      console.log(product,8989898989)
      let albumPicsJson = product.albumPicsJson ? product.albumPicsJson : '[]';
      albumPicsJson = JSON.parse(albumPicsJson);
      if(albumPicsJson.length>0&&product.productCategoryId!=75){
        albumPicsJson.forEach((item)=>{
          albumPicsJson.unshift({name:'全部图片',url:item.url})
        })
      }
      this.albumPicsByType = this.groupAlbumPics(albumPicsJson);
      
      if (this.albumPicsByType.length) {
        this.ntypeActive = this.albumPicsByType[0].type;
      } else {
        this.ntypeActive = '全部图片';
      }
      this.changeNtype(
        {
          type: this.ntypeActive,
        },
        'noScroll'
      );
    },
    getProductCategory() {
      getProductCategory(this.productCategoryId).then((res) => {
        if (res.code === 200) {
          const { data = {} } = res;
          this.productCategory = data;
          getKfList({
            game: this.productCategory.name,
            type: '咨询客服',
          }).then((res) => {
            if (res.code == 200) {
              let list = res.data || [];
              if (list.length) {
                this.kfObj = _.sample(list);
              }
            }
          });

         
        
          let custom = data.custom || '{}';
          custom = JSON.parse(custom);
          this.tabData=[{ title: '图片详情' }, { title: '交易须知' }]
          if (custom.teams && custom.teams.name) {
            this.tabData.push({ title: '交易群' },{ title: '卖家说' }, { title: '11' });
          } else {
            this.tabData.push({ title: '卖家说' },{ title: '11' });
          }
          this.saveCustom = custom;
          this.teams = custom.teams || {};
          this.albumPicsTypeOptions = custom.albumPicsTypeOptions || [];
          this.groupPics();
          let custom2 = data.custom2 || '{}';
          custom2 = JSON.parse(custom2);
          const { pc_conf = {} } = custom2;
          let tempList = [];
          
          if (pc_conf && pc_conf.area_1) {
            pc_conf.area_1.forEach((ele) => {
              const { attrName, name } = ele;
              const findIt = this.productAttributeValueList.find(
                (item) => item.productAttributeName === attrName
              );
              if (attrName == '区服') {
                let value = '暂无';
                if (findIt.value) {
                  if (findIt.value.indexOf('|') != -1) {
                    value = findIt.value.split('|')[1];
                  } else {
                    value = findIt.value;
                  }
                }
                tempList.push({
                  label: name,
                  value,
                });
              } else if (attrName == 'productSn') {
                tempList.push({
                  label: name,
                  value: this.shopDetailJson.productSn,
                });
              } else if (attrName == '议价') {
                tempList.push({
                  label: name,
                  value:
                    this.shopDetailJson.gameGoodsYijia == 1
                      ? '可议价'
                      : '不议价',
                });
              } else if (findIt) {
                let value = '暂无';
                if (findIt.value) {
                  if (findIt.value.indexOf('|') != -1) {
                    value = findIt.value.split('|')[1];
                  } else {
                    value = findIt.value;
                  }
                }
                tempList.push({
                  label: name,
                  value,
                });
              }
            });
          }
          if (pc_conf && pc_conf.area_2) {
            let area2TempList = [];
            pc_conf.area_2.forEach((ele) => {
              const { attrName, name } = ele;
              const findIt = this.productAttributeValueList.find(
                (item) => item.productAttributeName === attrName
              );
              if (attrName == '区服') {
                let value = '';
                if (findIt.value) {
                  if (findIt.value.indexOf('|') != -1) {
                    value = findIt.value.split('|')[1];
                  } else {
                    value = findIt.value;
                  }
                }
                if (value) {
                  area2TempList.push({
                    label: name,
                    value,
                  });
                }
              } else if (findIt) {
                let value = '';
                if (findIt.value) {
                  if (findIt.value.indexOf('|') != -1) {
                    value = findIt.value.split('|')[1];
                  } else {
                    value = findIt.value;
                  }
                }
                if (value) {
                  area2TempList.push({
                    label: name,
                    value,
                  });
                }
              }
            });
            this.area2List = area2TempList;
          }
          this.topAttrs = tempList;
        }
      });
    },
    iconFilter(v) {
      if (v.icon == '1') {
        return '../../static/push/jue.png';
      } else if (v.icon == '2') {
        return '../../static/push/price.png';
      } else if (v.icon == '3') {
        return '../../static/push/he.png';
      }
    },
    tedianFilter(text, item) {
      return util.tedianFilter(text, item);
    },
    backTopPage() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },
    // 申请加群
    addGroupFun(date) {
      if (date.link) {
        window.location.href = date.links;
      } else if (date.url) {
        this.dialogVisibleWxCode = true;
        this.wxUrlCode = date.url;
      }
    },
    imgPreviewSrc(v) {
      this.showViewer = true;
      this.imgViewer = v;
      console.log(v, this.arrDtPicForShow[v], 'ddddddd');
    },
    closeViewer() {
      this.showViewer = false;
    },

    showShareFun() {
      if (false && this.productCategoryId == 75) {
        this.showShare2 = true;
      } else {
        this.showShare = true;
      }
    },
    cloaseShare() {
      this.showShare = false;
      this.showShare2 = false;
    },
    similarFun() {
      this.similarDealDialogVisible = true;
      this.similarDealDialogId = this.shopDetailJson.id;
      this.similarDealDialogPrice = this.shopDetailJson.price;
    },
    similarDealDialogClone() {
      this.similarDealDialogVisible = false;
    },
    // 生成移动端分享二维码
    getCodePic() {
      this.qrcodeImg =
        linkConfig.linkH5url +
        '/pages/accountDetail/accountDetail?productId=' +
        this.productId;
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 108, //宽
        height: 108, //高
        text: this.qrcodeImg, //二维码内容
        color: {
          dark: '#1b1b1b', //前景色
          light: '#FFFCF9', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header_play');
      QRCode.toCanvas(msg, this.qrcodeImg, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        } else {
          const canvas = msg.getContext('2d');
          const logoWidth = 23;
          const logoHeight = 23;
          const logo = new Image();
          logo.src = '../../../static/imgs/qrcode_wechat2.png';
          logo.onload = function () {
            const x = (canvas.canvas.width - logoWidth) / 2;
            // 计算logo在二维码垂直方向的居中坐标
            const y = (canvas.canvas.height - logoHeight) / 2;
            // 将logo按照固定宽高绘制到二维码中间垂直居中位置
            canvas.drawImage(logo, x, y, logoWidth, logoHeight);
          };
        }
      });
    },
    // 详情数据切换
    chooseBottom(num) {
      if (num == 888) {
        getHelpDetail(161).then((res) => {
          if (res.code == 200) {
            this.introduceHtml = res.data.detailHtml;
          }
        });
      } else if (num == 999) {
        this.qqlist = this.teams.list || [];
      }
      this.bottomIndex = num;
    },
    changeMyTab(data) {
      this.tabType = data;
      console.log(data,1111111)
      switch (data) {
        case 0:
          this.chooseBottom(0);
          break;
        case 1:
          this.chooseBottom(888);
          break;
        case 2:
          console.log(this.tabData.length,2222222)
          if(this.tabData.length==4){
            this.chooseBottom(9999);
            this.getProductConsultationRecords()
          }else{
            this.chooseBottom(999);
          }
          
          break;
        case 3:
          this.chooseBottom(9999);
          this.getProductConsultationRecords()
          break;
      }
    },
    getProductConsultationRecords(){
      productConsultationRecords({productSn: this.productSn||this.shopDetailJson.productSn}).then((res)=>{
        // console.log(res)
        // let arr=[]
        // if(this.description){
        //   arr=[
        //   {
        //     consultQuestion:"卖家说",
        //     consultContent:this.description
        //   }
        // ]
        // }
        // arr.concat(res.data)
        // debugger
        this.consultationRecordsList=res.data
      })
    },
    // 复制操作
    copyVal(context) {
      // 创建输入框元素
      let oInput = document.createElement('input');
      // 将想要复制的值
      oInput.value = context;
      // 页面底部追加输入框
      document.body.appendChild(oInput);
      // 选中输入框
      oInput.select();
      // 执行浏览器复制命令
      document.execCommand('Copy');
      // 弹出复制成功信息
      this.$message.success('复制成功');
      // 复制后移除输入框
      oInput.remove();
    },
    popupCustom() {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
      } else {
        if (this.kfObj.wxurl && this.kfObj['微信号']) {
          this.dialogVisibleDaishou = true;
          this.$nextTick(() => {
            this.createWx();
          });
        } else {
          getMemberHisKFList({
            cateId: this.productCategoryId,
            productId: this.productId,
          }).then((res) => {
            if (res.code == 200) {
              const findKf = res.data;
              if (findKf) {
                const imcode = findKf;
                const sessionId = `p2p-${imcode}`;
                if (window.__xkit_store__) {
                  const { nim, store } = window.__xkit_store__;
                  m2kfTalk({
                    cateId: this.productCategoryId,
                    kfIM: imcode,
                  });
                  if (store.sessionStore.sessions.get(sessionId)) {
                    store.uiStore.selectSession(sessionId);
                  } else {
                    store.sessionStore.insertSessionActive('p2p', imcode);
                  }
                  this.$store.dispatch('ToggleProductCardId', this.productId);
                  this.$store.dispatch('ToggleIM', true);
                }
              } else {
                this.$store.dispatch('ToggleIM', true);
              }
            }
          });
        }
      }
    },
    createWx() {
      let qrcodeImg = `${this.kfObj.wxurl}`;
      let opts = {
        errorCorrectionLevel: 'H', //容错级别
        type: 'image/png', //生成的二维码类型
        quality: 0.3, //二维码质量
        margin: 0, //二维码留白边距
        width: 150, //宽
        height: 150, //高
        text: qrcodeImg, //二维码内容
        color: {
          dark: '#333333', //前景色
          light: '#fff', //背景色
        },
      };
      let msg = document.getElementById('QRCode_header_kfwx');
      QRCode.toCanvas(msg, qrcodeImg, opts, function (error) {
        if (error) {
          this.$message.error('二维码加载失败');
        }
      });
    },
    // 议价数据出现
    cutDailog() {
      if (isLogin()) {
        this.bargainVisible=true
        
        // this.loading = true;
        // topOfferPrice({
        //   productId: this.productId,
        // })
        //   .then((res) => {
        //     if (res.code == 200) {
        //       const { hisTopPrice, offerPrice } = res.data;
        //       this.offerPrice = hisTopPrice.offerPrice || {};
        //       this.maxPrice = offerPrice;
        //     }
        //   })
        //   .finally(() => {
        //     this.dialogVisible = true;
        //     this.loading = false;
        //   });
      } else {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
      }
    },
    // 确定议价
    addBindPrice() {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      if (!this.price || this.price < 0) {
        this.$message.error('请正确输入议价价格!');
        return;
      }
      if (this.price > this.shopDetailJson.price) {
        this.$message.error(`报价须小于商品原价${this.shopDetailJson.price}元`);
        return
      }
      if (this.price.length > 9) {
        this.$message.error('最多输入9位数字!');
        return;
      }
      if (!this.checked) {
        this.$message.error('请阅读并同意议价协议!');
        return;
      }

      this.loading = true;
      let data = {
        buyType: 3,
        memberPrice: this.price,
        productId: this.productId,
        // #ifdef H5
        sourceType: 1,
        // #endif
      };
      generateKKOrder2(data)
        .then((res) => {
          if (res.code == 200) {
            this.dialogVisible = false;
            this.$router.push({
              path: `/payOrder?from=myAssess&orderId=${res.data.id}`,
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getAttrTypeTxt(item, str) {
      const str1 = item
        .filter((ele) => [str].includes(ele.productAttributeName))
        .map((ele) => ele.value)
        .join(' ');
      return str1;
    },
    daojuDialogClone(){
      this.daojuNumVisible = false
    },
    // 立即下单购买
    confirnOrder() {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      if(this.saveCustom&&this.saveCustom.goodsType==="vgoods"){
        let pushAccountNum = this.getAttrTypeTxt(this.productAttributeValueList,'发布件数');
        if (pushAccountNum && pushAccountNum > 1) {
          let obj={
            productCategoryId:this.productCategoryId,
            id:this.productId, 
          }
          this.daojuDialogObj = obj;
          this.daojuCount = Number(pushAccountNum);
          this.daojuNumVisible = true;
          return;
        }
        
      }
      if(this.saveCustom.buyType==='kefu'){
        this.popupCustom();
        return
      }
      getCertDetail().then((res) => {
        if (res.code == 200) {
          if (res.data.defaultStatus !== 2) {
            this.dialogVisibleInden = true;
            return;
          } else {
            if (this.shopDetailJson.price >= 50000) {
              // 大于 5w 先找客服聊.这里暂时用咨询客服的客服
              this.popupCustom();
            } else {
              this.$router.push({
                path:
                  '/confirmOrder?productCategoryId=' +
                  this.productCategoryId +
                  '&productId=' +
                  this.productId,
              });
            }
          }
        }
      });
    },
    indentifyGo() {
      this.$router.push({
        path: '/account/approve',
      });
    },
    setFooterMark(product) {
      if (!isLogin()) {
        return;
      }
      if (product.price < 5000000) {
        readHistoryCreate({
          productId: product.id,
          categoryId: product.productCategoryId,
          productPrice: product.price,
        });
      }
    },
    // filterTopOpt(list) {
    //   this.topAttrs = list.filter((ele) => {
    //     return ele.sort > 20000 && ele.sort < 30000;
    //   });
    //   if (this.topAttrs.length > 4) {
    //     this.topAttrs = this.topAttrs.slice(0, 4);
    //   }
    // },
    groupAlbumPics(items) {
      items = items.map((ele) => {
        // 如果是 type 数据先转 name
        if (!ele.hasOwnProperty('name')) {
          const { type } = ele;
          // 如果是 1-5 取固定值，否则用type
          ele.name = OPTSOUT[type] || type || '';
        }
        return ele;
      });
      // 使用一个对象来根据type分类
      let groupedByType = {};
      // 遍历数组，将元素按type分类
      items.forEach((item) => {
        let type = item.name;
        if (type) {
          if (!groupedByType[type]) {
            // 如果这个type的数组还不存在，则初始化它
            groupedByType[type] = [];
          }
          // 将元素添加到对应type的数组中
          groupedByType[type].push(item.url);
        }
      });

      // 如果你想将groupedByType对象转换为数组形式（可选）
      let groupedArray = [];
      Object.keys(groupedByType).forEach((key) => {
        if (key != 0) {
          groupedArray.push({
            type: key,
            items: groupedByType[key],
          });
        }
      });
      let OPTSOUTSORT = {};
      this.albumPicsTypeOptions.forEach((ele, index) => {
        OPTSOUTSORT[ele.name] = index + 1;
      });
      groupedArray.sort((a, b) => {
        let a0 = OPTSOUTSORT[a.type];
        let b0 = OPTSOUTSORT[b.type];
        return a0 - b0;
      });
      const findAllPic = this.albumPicsTypeOptions.find(
        (ele) => ele.name == '全部图片'
      );
      if (findAllPic) {
        groupedArray.unshift({
          type: '全部图片',
          items: this.arrDtPic,
        });
      }

      if (!items || items.length == 0) {
        groupedArray = [];
      }
      return groupedArray;
    },
    getSkinAndHero() {
      if (this.wzryId&&this.wzUrl) {
        console.log(this.wzUrl,123123)
        getSkinAndHeroUrl(this.wzUrl).then((res) => {
          this.wzData = res;
          // this.wzData = listAll
          this.initwz();
          this.initwzHero();
        });
      }
    },

    // 游戏详情-初始化
    initGame() {
      this.loading = true;

      (this.productId
        ? getDetail(this.productId)
        : getDetailByCode({ productSn: this.productSn })
      ) // 根据编码或ID获取详情
        .then((res) => {
          this.loading = false;
          if (res.code == 200) {
            const { product, productAttributeList, productAttributeValueList } =
              res.data;

            this.productId = product.id;

            this.description = product.description;
            this.productCategoryId = product.productCategoryId;
            this.shopDetailJson = product;
            this.getProductCategory();
            this.arrDtPic = product.albumPics
              ? product.albumPics.split(',')
              : [];
            this.productAttributeValueList = productAttributeValueList;
            this.shopDetailJson.detail_options = this.mergeOptions(
              productAttributeList,
              productAttributeValueList
            );
            if (this.description) {
              this.zhsmList.push({
                name: '卖家说',
                value: this.description,
              });
            }

            // this.filterTopOpt(this.shopDetailJson.detail_options);
            this.getSkinAndHero();
            this.setFooterMark(product);
            let collects = localStorage.getItem('collects');
            this.collects = collects ? JSON.parse(collects) : [];
            if (this.collects.includes(this.productId)) {
              this.shopDetailJson.is_collect = 1;
            } else {
              this.shopDetailJson.is_collect = 0;
            }
            this.checkDesc();
          }
        });
    },
    checkDesc() {
      this.$nextTick(() => {
        const clientHeight = this.$refs.desc.clientHeight;
        if (clientHeight > 100) {
          this.getLineThreeClazz = 'spaceStart preBox text_linThree';
          this.showToggleDesc = true;
        } else {
          this.getLineThreeClazz = 'spaceStart preBox';
          this.showToggleDesc = false;
        }
      });
    },
    initwzHero() {
      this.arrDtPicForShow3 = [
        {
          name: '稀有英雄',
          list: [],
        },
        {
          name: '普通英雄',
          list: [],
        },
      ];
      this.wzData.heroList.forEach((ele) => {
        const { name } = ele;
        let obj = {
          ...ele,
          heroIcon: `https://images2.kkzhw.com/mall/statics/wzry/wzhero/${ele.heroId}.jpeg`,
        };
        if (WZHERO.includes(name)) {
          this.arrDtPicForShow3[0].list.push(obj);
        } else {
          this.arrDtPicForShow3[1].list.push(obj);
        }
      });
    },

    changeActiveType(type) {
      this.activeType = type;
      this.initwz();
    },
    changeWztype(item) {
      this.wztype = item;
      this.initwz();
    },
    initwz() {
      this.arrDtPicForShow2 = [];
      let tempList = [];
      tempList = this.wzData.skinList;
      if (this.activeName === 'skin') {
        if (this.activeType === 'career') {
          if (this.wztype != '全部') {
            tempList = tempList.filter((ele) => ele.heroType == this.wztype);
          }
          let tempObj = {};
          tempList.forEach((ele) => {
            let hero = tempObj[ele.heroId];
            let skinImg = `https://images2.kkzhw.com/mall/statics/wzry/wzskin/${ele.skinId}.jpeg`;
            let skinName = ele.skinName;
            if (!hero) {
              tempObj[ele.heroId] = {
                ...ele,
                heroIcon: `https://images2.kkzhw.com/mall/statics/wzry/wzhero/${ele.heroId}.jpeg`,
                list: [
                  {
                    skinImg,
                    skinName,
                  },
                ],
              };
            } else {
              hero.list.push({ skinImg, skinName });
            }
          });
          this.arrDtPicForShow2 = Object.keys(tempObj).map(
            (key) => tempObj[key]
          );
        } else {
          let tempClassTypeObj = {};
          tempList.forEach((ele) => {
            let { classTypeName = [] } = ele;
            if (classTypeName.length > 1) {
              classTypeName = [classTypeName[0]];
            }
            if (classTypeName.length === 0) {
              classTypeName = ['其他'];
            } else if (
              ![
                '传说品质',
                '史诗品质',
                '勇者品质',
                '勇者品质',
                '荣耀典藏',
              ].includes(classTypeName[0])
            ) {
              classTypeName[0] = '其他';
            }
            classTypeName.forEach((item) => {
              let skinImg = `https://images2.kkzhw.com/mall/statics/wzry/wzskin/${ele.skinId}.jpeg`;
              let { skinName } = ele;
              if (!tempClassTypeObj[item]) {
                tempClassTypeObj[item] = {
                  name: item,
                  list: [
                    {
                      skinImg,
                      skinName,
                    },
                  ],
                };
              } else {
                tempClassTypeObj[item].list.push({
                  skinImg,
                  skinName,
                });
              }
            });
          });
          this.arrDtPicForShow2 = Object.keys(tempClassTypeObj).map(
            (key) => tempClassTypeObj[key]
          );
          this.arrDtPicForShow2.sort((a, b) => {
            return a.list.length - b.list.length;
          });
        }
      }
    },
    mergeOptions(productAttributeList, productAttributeValueList) {
      productAttributeList.sort((a, b) => {
        return a.type - b.type;
      });
      productAttributeList.sort((a, b) => {
        return b.sort - a.sort;
      });
      let tempList = [];
      
      productAttributeList.forEach((ele) => {
        // if (ele.name == '营地ID') {
          // const findIt = productAttributeValueList.find((item) => {
          //   return item.productAttributeId === ele.id;
          // });
          // this.wzryId = findIt && findIt.value;
        // }
        if(ele.name == '账号元数据'){
          const findIt = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          this.wzUrl = findIt && findIt.value;
          this.wzryId=findIt && findIt.value;
          
        }
        if (ele.type === 1 || ele.type === 2) {
          const findV = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          if (findV && findV.value) {
            tempList.push({
              name: ele.name,
              value: this.formatValue(findV.value),
              sort: ele.sort,
            });
          }
        }
      });
      return tempList;
    },
    formatValue(value) {
      return value
        .replace(/[,]/g, '，')
        .replace(/\[核\]/g, '')
        .replace(/\[绝\]/g, '')
        .replace(/\[钱\]/g, '');
      // .replace(/[,]/g, '，')
      // .replace(
      //   /\[核\]/g,
      //   '<img class="tag_tedian_pic" src="../../../static/push/he.png" />'
      // )
      // .replace(
      //   /\[绝\]/g,
      //   '<img class="tag_tedian_pic" src="../../../static/push/jue.png" />'
      // )
      // .replace(
      //   /\[钱\]/g,
      //   '<img class="tag_tedian_pic" src="../../../static/push/price.png" />'
      // );
    },
    // 添加收藏
    collectAccount() {
      if (!isLogin()) {
        this.$router.push({
          path: '/login',
          query: {
            redirect: location.href,
          },
        });
        return;
      }
      if (this.shopDetailJson.is_collect != 1) {
        this.loading = true;
        productCollectionAdd({
          productCategoryId: this.shopDetailJson.productCategoryId,
          productCategoryName: this.shopDetailJson.productCategoryName,
          productId: this.productId,
          productName: this.shopDetailJson.name,
          productPic: this.shopDetailJson.pic,
          productPrice: this.shopDetailJson.price,
          productSn: this.shopDetailJson.productSn,
          productSubTitle: this.shopDetailJson.subTitle,
        }).then((res) => {
          this.loading = false;
          if (res.code == 200) {
            this.collects.push(this.productId);
            localStorage.setItem('collects', JSON.stringify(this.collects));
            this.$message.success('收藏成功！');
            this.initGame();
          }
        });
      } else {
        this.$confirm('您确定是否取消此收藏吗, 是否继续?', '提示', {
          closeOnClickModal: false,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.deletCollect();
        });
      }
    },
    // 取消收藏
    deletCollect() {
      this.loading = true;
      var json = {
        productId: this.productId,
      };
      productCollectionDetele(json).then((res) => {
        this.loading = false;
        if (res.code == 200) {
          this.collects = this.collects.filter((ele) => {
            return ele != this.productId;
          });
          localStorage.setItem('collects', JSON.stringify(this.collects));
          this.$message.success('已取消收藏！');
          this.initGame();
        }
      });
    },
  },
};
</script>
<style lang="scss">
.detail_collapse .el-collapse-item__header {
  background: transparent;
}
.detail_collapse .el-collapse-item__wrap {
  background: transparent;
}
.tooltip_list2 {
  width: 512px;
  flex-shrink: 0;
  border-radius: 24px;
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  padding: 32px 42px 33px 35px;
  opacity: 1 !important;
}
.playDetail_tab_btn {
  .tab-btn {
    flex: 0 !important;
    min-width: 164.5px !important;
    // background: #fff6eb !important;
    background: transparent !important;
    color: rgba(0, 0, 0, 0.4);
    text-align: center !important;
    font-family: 'PingFang SC' !important;
    font-size: 20px !important;
    font-style: normal !important;
    font-weight: 500 !important;
    height: 59.184px !important;
    letter-spacing: 0.8px !important;
    line-height: normal !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 24px !important;
    .right {
      &::before {
        // width: 0px;
        // left: 28px;
        // height: 17px !important;
        // background: red;
        background: #ffe1c3 !important;
      }
      &::after {
        background: #ffb74a !important;
        bottom: 1px !important;
        // bottom: 0.15vh !important;
        border-bottom-left-radius: 25px !important;
      }
    }
    .left {
      &::before {
        background: #ffe1c3 !important;
      }
      &::after {
        background: #ffb74a !important;
        bottom: 1px !important;
        // bottom: 0.15vh !important;
        border-bottom-right-radius: 25px !important;
      }

      // display: none;
    }
  }
  .tab-body {
    padding: 40px 38px 40.76px 38px !important;
    // background:
    border: 2px solid transparent !important;
    background-clip: padding-box, border-box !important;

    background-origin: padding-box, border-box !important;
    // background: linear-gradient(180deg, #ffe1c3 -7.11%, #ffffff 88.66%);
    background-image: linear-gradient(180deg, #ffe1c3 -7.11%, #ffffff 88.66%),
      linear-gradient(180deg, #ffb74a 10%, #fff 15%) !important;
  }

  .tab-head {
    // width: 492.86px !important;
    // width: 300px !important;
    display: flex !important;
    justify-items: flex-start !important;
    align-items: flex-end !important;
    position: relative !important;
    z-index: 9 !important;

    &.pd-left-0 {
      padding-left: 0 !important;
    }
    &.pd-right-0 {
      padding-right: 0 !important;
    }
  }

  .tab-btn:last-child {
    // background: #fff !important;
    // color: #fff !important;
    pointer-events: none;
    span {
      display: none !important;
    }
  }
  .tab-btn:last-child {
    color: transparent !important;
    pointer-events: none !important;
    background: transparent !important;
    .border-outside {
      background: #fdf5ed !important;
      border-radius: 24px 24px 0px 24px !important;
    }
  }
  .selected {
    color: #ff7a00;
    // text-align: center;
    /* Title/Large */
    font-family: YouSheBiaoTiHei !important;
    font-size: 32px !important;
    font-style: normal !important;
    font-weight: 400 !important;
    background: #ffe1c3 !important;
    border: 2px solid #ffb74a !important;
    border-bottom: 0px !important;
    bottom: -3px !important;
    height: 81.874px !important;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px !important;
    border-radius: 24px 24px 0px 0px !important;
    .border-outside {
      margin-top: -7px !important;
    }
    &.no-top-left {
      border-top-left-radius: 0 !important;

      &::before {
        border-top-left-radius: 0 !important;
      }
    }

    &.no-top-right {
      border-top-right-radius: 0 !important;
    }
  }
  .tab-body {
    padding: 40px 38px 40.76px 38px !important;
    // background:
    border: 2px solid transparent !important;
    background-clip: padding-box, border-box !important;

    background-origin: padding-box, border-box !important;
    // background: linear-gradient(180deg, #ffe1c3 -7.11%, #ffffff 88.66%);
    background-image: linear-gradient(180deg, #ffe1c3 -7.11%, #ffffff 88.66%),
      linear-gradient(180deg, #ffb74a 10%, #fff 15%) !important;
    &.no-top-left {
      border-top-left-radius: 0 !important;

      &::before {
        border-top-left-radius: 0 !important;
      }
    }

    &.no-top-right {
      border-top-right-radius: 0 !important;
    }
  }
}
</style>

<style lang="scss" scoped>
.el-table-border-style {
  border-color: #e9e9e9 !important;
  // /deep/ .el-table {
  //   background-color: red !important;
  // }
  &::before {
    background-color: #e9e9e9 !important;
  }
  &::after {
    background-color: #e9e9e9 !important;
  }
  /deep/ .el-table__row td {
    border-color: #e9e9e9 !important;
  }
  /deep/ td.el-table__cell {
    border-color: #e9e9e9 !important;
  }
}

.playDetail_tab_btn {
  &:not(.selected) {
    width: 100%;
    height: 100%;
    background: none !important;
    border-radius: 24px 24px 0px 0px !important;
  }
  /deep/.border-outside {
    width: 100%;
    height: 100%;
    height: 59.184px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 24px !important;
    // border-radius: 24px 24px 0px 0px !important;
    background: rgb(253, 245, 237) !important;
  }
}

// FFE1C3
.descBox {
  padding-left: 10px;
  font-size: 16px;
  color: #666;
  font-weight: 500;
  position: relative;
}
.arrow {
  font-size: 18px;
  position: absolute;
  right: 0;
  bottom: 5px;
  cursor: pointer;
}
.preBox {
  white-space: pre-wrap;
  word-break: break-all;
  cursor: pointer;
  margin: 0;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 171.429% */
  letter-spacing: 0.56px;
  padding: 0px !important;
}
/deep/ .el-table .el-table__empty-block {
  display: none;
}
/deep/ .el-table .el-table__cell {
  padding: 0px 0;
  color: #222;
  font-family: 'PingFang SC';
}
/deep/ .el-tabs__item:hover,
/deep/ .el-tabs__item.is-active {
  color: #ff6917;
}
/deep/ .el-tabs__active-bar {
  background: #ff6917;
}
.hero_box {
  font-size: 14px;
  .name {
    width: 150px;
    border-radius: 10px;
    background: #f4f4f4;
    text-align: center;
    margin: 10px 0 10px 0;
    font-size: 12px;
  }
  .heros {
    flex-wrap: wrap;
    .hero_warp {
      position: relative;
      .hero_name {
        position: absolute;
        bottom: 0;
        text-align: center;
        color: #fff;
        width: 100%;
        line-height: 20px;
        font-size: 12px;
      }
    }
    .hero {
      width: 65px;
      height: 66px;
      margin: 5px 5px 0 0;
    }
  }
}
.skin_box {
  align-items: flex-start;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
  .hero {
    .imgbox {
      .img {
        width: 66px;
        height: 66px;
      }
    }
  }
  .skin {
    position: relative;
    .skinimg {
      width: 70px;
      height: 110px;
    }
    .skinname {
      position: absolute;
      color: #fff;
      font-size: 12px;
      width: 100%;
      bottom: 0px;
      text-align: center;
    }
  }

  .name {
    font-size: 12px;
    line-height: 18px;
    color: #333;
  }
  .namenum {
    font-size: 12px;
    line-height: 18px;
    color: #333;
  }
  .skin {
    margin-left: 10px;
  }
}
.skin_box2 {
  font-size: 14px;
  .skins {
    flex-wrap: wrap;
    .skin_warp {
      position: relative;
      margin: 10px 10px 0 0;
      .skinname {
        position: absolute;
        color: #fff;
        font-size: 12px;
        width: 100%;
        bottom: 0px;
        text-align: center;
      }
    }
  }
  .name {
    width: 100px;
    border-radius: 10px;
    background: #f4f4f4;
    text-align: center;
    margin: 10px 0 0 0;
    font-size: 12px;
  }
  .skin {
    width: 70px;
    height: 110px;
  }
}
.wztype {
  font-size: 14px;
  font-weight: 400;
  .item {
    margin-right: 10px;
    cursor: pointer;
  }
  .active {
    font-weight: 700;
  }
}
.type_btn {
  background: #f4f4f4;
  border-radius: 8px;
  color: #666;
  cursor: pointer;
  display: block;
  font-weight: 400;
  padding: 7px 22px;
  text-align: center;
  font-size: 14px;
  line-height: 22px;
  margin-right: 20px;
}
.type_btn.active,
.type_btn:hover {
  background: #fee;
  color: #ff6917;
  font-weight: 500;
}
.ntypeBox {
  position: sticky;
  top: 34.28px;
  z-index: 9;
  background-color: #fff;
  padding: 9px 8px;
  width: 100%;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  border-radius: 30px;
  height: 68px;
  gap: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow-x: auto; /* 修改为横向滚动 */
  &::-webkit-scrollbar {
    display: none; /* 隐藏谷歌浏览器的滚动条 */
  }
  /* 隐藏其他浏览器的滚动条 */
  & {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* Internet Explorer 和 Edge */
  }
}
.ntype {
  // height: 50px;
  cursor: pointer;
  // line-height: 50px;
  // padding: 0 48px;
  min-width: 150px;
  border-radius: 24px;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 1.28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--btn-background-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.ntype.active {
  color: #fff !important;
  background-clip: none !important;
  -webkit-background-clip: none !important;
  -webkit-text-fill-color: #fff !important;
  height: 50px;
  line-height: 50px;
  background: var(--btn-background) !important;
  background-size: cover;
}

/deep/ .el-table__header {
  display: none;
}
/deep/ .introduce_box {
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
/deep/ .tag_tedian_pic {
  width: 20px;
  height: 20px;
  margin-left: 3px;
  position: relative;
  top: -2px;
}
.attr_box {
  position: relative;
  z-index: 9;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-weight: 400;
  padding-top: 21px;
  border-top: 1px solid #ffeed5;
  // background: url(../../../static/priBg.png) repeat center top;
  // background-size: cover;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;

  .attr_one {
    // min-width: 26%;
    // width: 33.33333%;
    flex: 0 0 calc(100% / 3);
    // text-align: right;
    // padding: 14px 0;
    overflow: hidden;
    margin-bottom: 10px;
    // margin-left: 47px;
    color: rgba(0, 0, 0, 0.4);
    font-family: 'PingFang SC';
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.56px;

    &:nth-child(1) {
      margin-left: 0px;
    }
    &:nth-child(4) {
      margin-left: 0px;
    }
  }
  .attr_one1 {
    // min-width: 26%;
    // width: 33.33333%;
    flex: 0 0 calc(100% / 2);
    // text-align: right;
    // padding: 14px 0;
    overflow: hidden;
    margin-bottom: 10px;
    // margin-left: 47px;
    color: rgba(0, 0, 0, 0.4);
    font-family: 'PingFang SC';
    font-size: 14px;
    line-height: 24px;
    letter-spacing: 0.56px;

    &:nth-child(1) {
      margin-left: 0px;
    }
    &:nth-child(4) {
      margin-left: 0px;
    }
  }
  .attr_value {
    color: #505050;

    /* 新/文本/中号加强 */
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }
}
.s_tit_one {
  position: absolute;
  left: 46px;
  bottom: 3.92px;
}
.game_Spic_box {
  height: 234px;
  border: 2px solid #ffddbe;
  background: #fff;
  border-radius: 16px;
  display: flex;
  // align-items: center;
  margin-top: 19.08px;
  padding: 11px 0px 21px 16px;
}
.msk_body_logo_bk {
  width: 267.124px;
  height: 281px;
  position: absolute;
  // top: 50%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 148px;
  z-index: -1;
}
.game_share_descriptive {
  margin-top: 30px;
  width: 597px;
  .title {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.8px;
  }
  .description {
    height: 60px;
    color: #1b1b1b;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px; /* 187.5% */
    letter-spacing: 0.64px;
    margin-top: 5px;
  }
}
.game_share_price {
  .game_share_price_box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-start;
  }
  .price_title {
    color: #000;
    font-family: 'PingFang SC';
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0.8px;
    margin-left: 2px;
  }
  .price {
    font-family: Inter;
    font-size: 48px;
    font-style: normal;
    font-weight: 600;
    line-height: 103%; /* 49.44px */
    letter-spacing: -1.2px;
    background: var(--btn-background-gradient);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-top: 5px;
    margin-left: 5px;
  }
  .game_share_QrCode {
    width: 88px;
    height: 88px;
    margin-right: 26px;
    position: relative;
  }
}
.game_Spic {
  width: 217px;
  height: 202px;
}
.game_Spic img {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  object-fit: fill;
}
.game_Spic_share_title_box {
  height: 202px;
  margin-left: 28px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  flex: 1;
  .title {
    color: #000;
    font-family: YouSheBiaoTiHei;
    font-size: 24px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px; /* 85.714% */
    text-indent: -16px;
    margin-top: 8px;
    .gameSn {
      margin-left: -30px;
    }
  }
  .share_attr_box {
    display: flex;
    flex-wrap: wrap;
  }
  .share_attr_one {
    width: 50%;
    color: #1b1b1b;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.64px;
    margin-top: 10px;
    .attr_value {
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0.8px;
    }
  }
}
.m_tit_main {
  font-size: 16px;
  color: #333;
  font-weight: 600;
  text-align: center;
  padding-bottom: 10px;
}
.m_tit_sub {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  word-break: break-all;
  -webkit-line-clamp: 4;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  font-size: 14px;
  width: 64%;
  align-items: flex-start;
  margin: 0 auto;
  margin-bottom: 15px;
  margin-top: 5px;
  color: #404140;
  line-height: 24px;
}
.share_priceBox {
  font-size: 20px;
  text-align: center;
  color: #fff;
  box-sizing: border-box;
  border-radius: 18px;
  background: #784a01;
  padding: 6px 25px;
  display: inline-block;
  margin: 0 auto;
  margin-bottom: 20px;
}
.share_price_mid {
}
.code_wrap {
  width: 180px;
  height: 180px;
  box-sizing: border-box;
  padding: 15px;
  margin: 0 auto;
  background: url(../../../static/share_page_qrcode_bg.png) no-repeat center top;
  background-size: cover;
}
.tit_two {
  font-size: 12px;
  color: #272727;
  padding-top: 20px;
}
.copy {
  font-size: 14px;
}
.goodsDt_wrap {
  box-sizing: border-box;
  // padding: 20px;
  border-radius: 24px;
}
.goodsDt_pic {
  width: 555px;
  height: 312px;
  position: relative;
  overflow: hidden;
  border-radius: 24px;
}
.soled_picDetail {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 10;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.5);
}
.soled_picDetail > img {
  display: block;
  margin: 0 auto;
  width: 160px;
}
.goodsDt_right {
  width: 543px;
  flex-shrink: 0;
  font-size: 18px;
  color: #222222;

  height: 312px;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  //   display: flex;
  // flex-direction: column;
  // justify-content: space-between;
}
.goodsDt_right_bk_img {
  width: 85.7px;
  height: 90.152px;
  position: absolute;
  top: 19px;
  right: 39.422px;
  z-index: 0;
}
.goodsDt_right_bk_img2 {
  width: 129.284px;
  height: 136px;
  position: absolute;
  top: 41px;
  right: 47.72px;
  z-index: 0;
}
.goodsDt_right_text_ab {
  color: #f5f5f5;
  font-family: YouSheBiaoTiHei;
  font-size: 56px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px; /* 67.857% */
}
.goodsDt_right_text_ab1 {
  position: absolute;
  top: 49px;
  right: 33px;
  z-index: 0;
}
.goodsDt_right_text_ab2 {
  position: absolute;
  top: 50%;
  right: 33px;
  z-index: 0;
  transform: translateY(-50%);
}
.goodsDt_price {
  width: 100%;
  // background: url(../../../static/priBg.png) no-repeat center top;
  // background-size: cover;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: 400;
  color: #333333;
}
.goodsDt_priceNum {
  font-size: 28px;
  font-weight: 600;
  color: #f7423f;
  margin-left: 30px;
}
.goodsDt_new_price {
  color: #222;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  margin-bottom: 13px;
  letter-spacing: 1.28px;
  line-height: normal;
}
.goodsDt_new_priceNum {
  font-size: 32px;
  font-weight: 600;
  font-family: Inter;
  color: #ff7a00;
  padding-bottom: 28px;
  letter-spacing: -0.8px;
  line-height: 103%;
}

.plDt_btn {
  margin-left: 8.57px;
  width: 148px;
  background: linear-gradient(90deg, #ff9600, #ff6700);
  border-radius: 21px;
  padding: 11px 0;
  font-size: 16px;
  color: #ffffff;
  cursor: pointer;
}
.plDt_btn i {
  margin-right: 4px;
}
.plain_btn {
  color: #ff6917;
  border: 1px solid #ff6917;
  box-sizing: border-box;
  background: #fff4ef;
}
.plain_btn.disabled {
  background: #f3f3f3;
  border: 1px solid #ccc;
  color: #666;
}
.goodsDt_collect {
  font-size: 14px;
  color: rgba(34, 34, 34, 0.4);
  font-family: 'PingFang SC';
  cursor: pointer;
  letter-spacing: 0.56px;
  font-weight: 500;
  display: flex;
  align-items: center;
  // margin-left: 16.283px;
}
.goodsDt_collect i {
  font-size: 20px;
  font-weight: 600;
}
.goodsDt_htmlWrap {
  // margin: 10px 0 20px;
  // background: #fff;
  // padding-bottom: 52.277px;
  margin-bottom: 80px;
}
.goodsDt_htmlType {
  border-bottom: 1px solid #dcdcdc;
  font-size: 16px;
  color: #333333;
  overflow-y: scroll;
}
.goodsDt_htmlType::-webkit-scrollbar {
  height: 4px;
}
.goodsDt_htmlType::-webkit-scrollbar-thumb {
  background: #999;
  border-radius: 5px;
}
.goodsDt_htmlItem {
  cursor: pointer;
  transition: all 0.3s;
  padding: 18px 32px;
  flex-shrink: 0;
  font-size: 1.2rem;
}
.goodsDt_htmlItem.active,
.goodsDt_htmlItem:hover {
  background: #ff6917;
  color: #fff;
}
.goodsDt_htmlCon {
  box-sizing: border-box;
  // padding: 20px;
  font-size: 19px;
  font-weight: 600;
  letter-spacing: 1px;
  color: #4d4444;
  line-height: 32px;
}
.cutDt_pic {
  width: 179.11px;
  height: 111.79px;
  border-radius: 10.28px;
  margin-right: 12px;
  overflow: hidden;
  flex-shrink: 0;
}
.cutDt_body_name {
  font-family: 'PingFang SC';
  font-size: 17.14px;
  color: #222222;
  // margin-bottom: 12.855px;
  line-height: normal;
  font-weight: 500;
}
.dialog_account_price {
  color: #9a9a9a;
  font-family: 'PingFang SC';
  font-size: 13.712px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  span {
    color: #969696;
  }
}
.cutDt_body {
  height: 111.79px;
  font-size: 12px;
  color: #909090;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}
.cutDt_body1 {
  height: 111.79px;
  font-size: 12px;
  color: #909090;
  // display: flex;
  // justify-content: space-between;
  // flex-direction: column;
}
.cutDt_price {
  color: #ff720c;
  font-family: Inter;
  font-size: 20.56px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  display: flex;
  align-items: center;
}
.cutDt_pushWrap {
  width: 289.66px;
  height: 41px;
  // border: 1px solid #ff6716;
  background: var(--btn-background-gradient);
  border-radius: 20.56px;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  position: relative;
  text-decoration: none;
  display: flex;
  align-items: center;
  &::before {
    content: '' !important;
    position: absolute;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    border-radius: 20.56px;
    background: #fffcf9;
    z-index: 0;
    margin: 3px;
    z-index: 1;
  }
}
.cutDt_push_ipt {
  height: 40px;
  color: #ff720c;
  border: none;
  background: transparent;
  width: 70%;
  outline: none;
  text-indent: 17.14px;
  font-family: Inter;
  font-size: 20.56px;
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  letter-spacing: -0.96px;
  position: relative;
  z-index: 2;
  &::placeholder {
    font-weight: 400;
    font-family: 'PingFang SC';
    color: rgba(0, 0, 0, 0.4);
  }
  &::-webkit-input-placeholder {
    font-weight: 400;
    font-family: 'PingFang SC';
    color: rgba(0, 0, 0, 0.4);
  }
  &::-moz-input-placeholder {
    font-weight: 400;
    font-family: 'PingFang SC';
    color: rgba(0, 0, 0, 0.4);
  }
  &::-ms-input-placeholder {
    font-weight: 400;
    font-family: 'PingFang SC';
    color: rgba(0, 0, 0, 0.4);
  }
}
.cutDt_push_ipt:focus {
  border: none;
}
.cutDt_push_btn {
  font-family: YouSheBiaoTiHei;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 85.714% */
  letter-spacing: 0.56px;
  text-align: center;
  color: #ff720c;
  cursor: pointer;
  margin-left: 20.56px;
  position: relative;
  z-index: 2;
}
.cut_cnver {
  margin: 24px 0px 7.713px 0px;
  color: #1b1b1b;
  font-family: YouSheBiaoTiHei;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
.cut_conHtml {
  color: #969696;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px; /* 175% */
  margin-bottom: 12.855px;
}
.iden_text {
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  margin-bottom: 30px;
}
.collectIcon {
  font-size: 22px;
  margin-top: -3px;
}
.collectIcon.active {
  color: #ff6917;
  font-size: 24px;
}
.fd_pic {
  width: 100%;
  margin-top: 20px;
  cursor: pointer;
}
.mid_chennuo {
  font-size: 14px;
  color: transparent;
  font-family: YouSheBiaoTiHei;
  font-weight: 400;
  letter-spacing: 0.28px;
  margin-left: 18px;
  background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.mid_chennuo_item {
  padding: 0 6px;
}
.mid_chennuo_item:nth-child(1) {
  padding-left: 0px;
}
.mid_chennuo_item img {
  width: 28px;
}
.pyDt_showItem {
  width: 24%;
  padding: 18px 0;
  text-align: center;
  font-size: 16px;
  letter-spacing: 1px;
  color: #666;
  border-radius: 8px;
  border: 1px solid #dcdcdc;
  cursor: pointer;
  transition: all 0.3s;
}
.pyDt_showItem:hover {
  background: #fff4ef;
  color: #ff6917;
  border: 1px solid #ff6917;
}
.zhuangbei_tit {
  font-size: 18px;
  color: #333;
  font-weight: 600;
  padding: 18px 0 5px;
  text-align: center;
}
.goodsTitle {
  color: #4d4444;
  padding: 0 0.5rem 0;
  display: inline-block;
  border-radius: 15px;
  background: linear-gradient(
    -45deg,
    #f6e736,
    #fddb14,
    #fcd610,
    #fdb40a,
    rgb(250, 98, 51),
    #fd8e15,
    #fdb40a
  );

  background-size: 600% 600%;
  animation: gradientBG 5s ease infinite;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
/* 折叠展开效果 */
.wrapper {
  background-color: #ffffff;
  display: flex;
  overflow: hidden;
  width: 1200px;
  margin-left: -20px;
  margin-top: -10px;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 10px 20px 40px #e4e4e4, -20px -20px 60px #ffffff;
}
.text {
  font-size: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  position: relative;
  line-height: 1.5;
  max-height: 4.5em;
  transition: 0.3s max-height;
}
.nozhedie .text {
  max-height: 10em;
}
.text::before {
  content: '';
  height: calc(100% - 26px);
  float: right;
}
.text::after {
  content: '';
  width: 999vw;
  height: 999vw;
  position: absolute;
  box-shadow: inset calc(100px - 999vw) calc(30px - 999vw) 0 0 #fff;
  margin-left: -100px;
}
.btn {
  position: relative;
  float: right;
  clear: both;
  margin-left: 20px;
  font-size: 16px;
  padding: 0 8px;
  background: #ff9000;
  line-height: 24px;
  border-radius: 4px;
  color: #fff;
  cursor: pointer;
  /* margin-top: -30px; */
}
.btn::after {
  content: '展开';
}
.exp {
  display: none;
}
.exp:checked + .text {
  max-height: none;
}
.exp:checked + .text::after {
  visibility: hidden;
}
.exp:checked + .text .btn::before {
  visibility: hidden;
}
.exp:checked + .text .btn::after {
  content: '收起';
}
.btn::before {
  content: '...';
  position: absolute;
  left: -5px;
  color: #333;
  transform: translateX(-50%);
}
.qqList_box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 125.122px;
}
.goodsDt_qqGroup {
  width: 527px;
  height: 174.828px;
  border-radius: 25.71px;
  padding: 24px 0px 24.8px 33.42px;
  display: flex;

  background: #fffcf9;
  box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);

  background: radial-gradient(
      31.25% 236.33% at 96.59% 31.25%,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    ),
    radial-gradient(
      39.2% 181% at 5.68% 100%,
      rgba(246, 251, 34, 0.306) 0%,
      rgba(255, 158, 69, 0) 100%
    ),
    radial-gradient(
      71.74% 117.31% at 32.95% 0%,
      rgba(255, 137, 137, 0.828) 21.25%,
      rgba(255, 169, 106, 0.513) 88.62%
    ),
    radial-gradient(
      92.05% 200% at 94.89% -132.81%,
      #ff7a00 67.59%,
      rgba(255, 199, 0, 0.38) 100%
    ),
    linear-gradient(0deg, #fff500, #fff500);
  position: relative;
  z-index: 1;
  text-decoration: none;
  &::before {
    content: '' !important;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 24.71px;
    background: #fffcf9;
    line-height: 39px;
    z-index: 0;
    margin: 1px;
    position: absolute;
    box-shadow: 1px 2px 3px 0px rgba(0, 0, 0, 0.05);
  }
  .qqGroup_logo {
    width: 119.758px;
    height: 125.979px;
    margin-right: 3.26px;
    position: relative;
    z-index: 2;
  }

  .qqGroup_box_rigth {
    position: relative;
    z-index: 2;
    min-width: 215.14px;
    text-align: center;
    .title {
      background: linear-gradient(180deg, #ffb74a 0%, #ff7a00 100%);

      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      font-family: YouSheBiaoTiHei;
      font-size: 20.567px;
      // height: 26.57px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      white-space: nowrap;
      margin-top: 7.714px;
    }
    .qqNum {
      color: #ff7a00;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 20.567px;
      // height: 29.13px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin-top: 7.713px;
    }
    .goodsDt_qqGroup_niceButton {
      width: 146.547px;
      height: 42.8575px;
      font-size: 16px;
      cursor: pointer;
      text-align: center;
      font-family: 'PingFang SC';
      display: inline-block;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0.72px;
      border-radius: 60px;
      color: #fff;
      // background: #000;
      line-height: 42.8575px;
      margin-top: 12px;
      // background: url(../../../static/imgs/playDetail_qq_btn_bk.png);
      // background-size: 100% 100%;
      background: var(--btn-background-gradient);
      border: 1px solid #ffddbe;
      box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
    }

    // .goodsDt_qqGroup_niceButton:hover {
    //   background-color: #ffa62d;
    // }
    // .goodsDt_qqGroup_niceButton:active {
    //   background-color: #ff6917;
    //   box-shadow: 0 5px #afafaf;
    //   transform: translateY(2px);
    // }
  }
  // padding: 44.564px 64.275px 125.122px 64.275px;
}

.detailAcc_warp {
  background: #fff;
  width: 1200px;
  flex-shrink: 0;
  box-sizing: border-box;
  padding: 40px;
  border-radius: 24px;
  // margin-left: -20px;
  // margin-top: -10px;
  // margin-bottom: -10px;
}
.detailAcc_title {
  color: #ff7a00;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 38px;
  // padding-left: 10px;
  // border-left: 7px solid #ff6917;
  margin-bottom: 24px;
}
.detailAcc_item_box {
  align-items: flex-start;
  padding-top: 15px;
}
.detailAcc_item_left {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  flex-shrink: 0;
  position: relative;
}

.detailAcc_item_right {
  width: 1055px;
  flex-shrink: 0;
  flex-wrap: wrap;
  padding-left: 10px;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}
.icon_tedian {
  width: 17px;
  height: 17px;
  margin-left: 4px;
}
.tedian_desction {
  font-size: 14px;
  font-weight: 500;
}
.tedian_desction_pic {
  width: 22px;
  margin-right: 6px;
}
.tedian_desction_text {
  font-size: 16px;
  font-weight: 500;
}
.tedian_desction_text.one {
  color: #ffbb05;
}
.tedian_desction_text.two {
  color: #ff134b;
}
.tedian_desction_text.three {
  color: #08a2ff;
}
// .toggle-div {
//   /* 固定在页面底部，高度为100px */
//   position: fixed;
//   bottom: 0;
//   left: 50%;
//   z-index: 10;
//   text-align: center;
//   margin-left: -3px;
//   /* 如果需要，可以添加过渡效果 */
//   transition: transform 1s ease;
//   /* 初始时将其隐藏（通过transform移动出视口） */
//   transform: translateX(-50%) translateY(100%);
// }
// .toggle-div.is-visible {
//   transform: translateX(-50%) translateY(0);
// }
.toggle-div {
  /* 固定在页面底部，高度为100px */
  position: fixed;
  bottom: 0;
  width: 100%;
  height: 88.27px;
  display: flex;
  align-items: center;
  z-index: 10;
  text-align: center;
  margin-left: -3px;
  /* 如果需要，可以添加过渡效果 */
  transition: transform 1s ease;
  /* 初始时将其隐藏（通过transform移动出视口） */
  transform: translateX(-50%) translateY(100%);
  margin: auto;
  transform: translateY(100%);
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  .toggle-div.is-visible {
    transform: translateX(-50%) translateY(0);
  }
}
.footerStickyBox {
  min-width: 1200px;
  max-width: 1200px;
  width: 1200px;
  padding: 0px 31.709px;
  margin: 0 auto;
}
.toggle-div.is-visible {
  transform: translateY(0);
}
// /deep/.nav_container {
//   margin-top: 84px !important;
// }
.playDetail_page_header_box {
  border-radius: 24px;
  background: linear-gradient(180deg, #ffe4c9 -21.57%, #fff -7.76%);
}
.playDetail_page_header_imgText_box {
  padding: 40px 40px 20px 40px;
}
.goodsDt_title {
  // height: 84px;
  color: #222;
  font-family: 'PingFang SC';
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 28px;
  letter-spacing: 0.8px;
}
.goodsDt_right_goodsDt_price {
  width: 543px;
  height: 215px;
  // height: 234.8px;
  margin-top: 13px;
  background: #ccc;
  border-radius: 24px;
  background: linear-gradient(94.41deg, #ffddbe 5%, #ffc085 97.55%);
  position: relative;
  padding: 24px 43px 18px 32px;
}
.goodsDt_right_goodsDt_price::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 22px;
  z-index: 0;
  margin: 3px;
  background: #fffcf9;
}
.goodsDt_right_goodsDt_price2 {
  width: 543px;
  height: 214px;
  // height: 234.8px;
  margin-top: 13px;
  background: #ccc;
  border-radius: 24px;
  background: #fbfbfb;
  border: 3px solid rgba(166, 166, 166, 0.4);
  position: relative;
  padding: 24px 43px 18px 32px;
}
.border_table /deep/ .el-table__row {
  &:nth-child(1) {
    td {
      &:nth-child(1) {
        border-radius: 0px 0px 0px 24px;
      }
      &:nth-child(2) {
        border-radius: 0px 0px 24px 0px;
      }
    }
  }
  td:nth-child(1) {
    color: #222;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    letter-spacing: 1.6px;
    // margin-left: 20px;
    div {
      padding-left: 24.36px;
    }
  }
  td:nth-child(2) {
    color: #222;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.72px;
    // margin-left: 20px;
    div {
      padding-left: 58.78px;
      padding-right: 55px;
    }
  }
}
.border_top_table /deep/ .el-table__row {
  &:nth-child(1) {
    td {
      &:nth-child(1) {
        border-radius: 24px 0px 0px 0px;
      }
      &:nth-child(2) {
        border-radius: 0px 24px 0px 0px;
      }
    }
  }

  td:nth-child(1) {
    color: #222;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    letter-spacing: 1.6px;
    // margin-left: 20px;
    div {
      padding-left: 24.36px;
      // position: absolute;
      // top: 15px;
    }
  }
  td:nth-child(2) {
    color: #222;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    letter-spacing: 0.72px;
    // margin-left: 20px;
    div {
      padding-left: 58.78px;
      padding-right: 55px;
    }
  }
}
.playDetailPreviewImg {
  width: 100%;
  height: 100%;
  position: absolute;
  z-index: 999999;
  top: 0px;
  left: 0px;
  background: rgba(0, 0, 0, 0.65);
  backdrop-filter: blur(10px);
}
//购买按钮
.buyNow {
  // width: 148px;
  padding: 0 48px;
  height: 46px;
  cursor: pointer;
  // background: url(../../../static/imgs/playDetail_qq_btn_bk.png);
  // background-size: 100% 100%;
  background: var(--btn-background-gradient);
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  letter-spacing: 0.64px;
  font-weight: 500;
  line-height: normal;
  // letter-spacing: 0.72px;
  margin-left: 12px;
  border-radius: 60px;
}
.buyNow:hover {
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient-hover);
}
// 求降价
.price_reduction {
  // width: 130.28px;
  padding: 0 48px;
  height: 46px;
  cursor: pointer;
  // background: url(../../../static/imgs/playDetail_qq_btn_bk.png);
  // background-size: 100% 100%;
  background: var(--btn-background-gradient);
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  letter-spacing: 0.64px;
  line-height: normal;
  border-radius: 60px;
  margin-left: 12px;
  // letter-spacing: 0.72px;
}
.price_reduction:hover {
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  background: var(--btn-background-gradient-hover);
}
//咨询客服
.customer_service {
  // width: auto;
  height: 46px;
  width: 162px;
  background: var(--btn-background-gradient) !important;
  position: relative;
  border: none;
  border-radius: 60px;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  cursor: pointer;
  text-decoration: none !important;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}
.customer_service span {
  position: relative;
  z-index: 2;
  background: var(--btn-background-gradient);
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text; /* 将背景裁剪到文字区域 */
  -webkit-text-fill-color: transparent; /* 设置文字颜色为透明 */
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  display: block;
}
.customer_service::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 60px;
  background: #fff;
  z-index: 1;
  margin: 3px;
  -webkit-border-radius: 60px;
  -moz-border-radius: 60px;
  -ms-border-radius: 60px;
  -o-border-radius: 60px;
}
.bargain_right_title {
  color: #ff720c;
  font-family: YouSheBiaoTiHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
}
.bargain_img_box {
  margin-top: 29.13px;
  border: 1px solid #ffb74a;
  background: #fff;
  border-radius: 13.712px;
  height: 143.119px;
  padding: 0px 27.424px 0px 15.426px;
}
.bargainDialog /deep/ .dialogBk {
  top: 160.259px;
}
.cut_checkout_box {
  color: rgba(0, 0, 0, 0.4);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  .cut_checkout_box_text {
    color: #ffb74a;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
  }
}
.cutDt_submit {
  width: 113.981px;
  height: 42.85px;
  color: #fff;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 15.426px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.72px;
  border: 1px solid #ffddbe;
  background: rgba(0, 0, 0, 0.4);
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  border-radius: 51.42px;
  cursor: pointer;
}
.cutDt_submit:hover,
.cutDt_submit:active {
  background: var(--btn-background-gradient);
  border: 1px solid #ffddbe;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
}

.tab_playDetail0::v-deep .tab-btn:nth-child(2) {
  border-left: 7.23px solid #fdf5ed !important;
  .border-outside {
    border-radius: 24px 24px 0px 17px !important;
  }
}
.tab_playDetail0::v-deep .tab-btn:nth-child(3) {
  border-left: 7.23px solid #fdf5ed !important;
  .border-outside {
    border-radius: 24px 24px 0px 0px !important;
  }
}
.tab_playDetail1::v-deep .tab-btn:nth-child(1) {
  border-right: 7.23px solid #fdf5ed !important;
  .border-outside {
    border-radius: 24px 24px 17px 0px !important;
  }
}
.tab_playDetail1::v-deep .tab-btn:nth-child(3) {
  border-left: 7.23px solid #fdf5ed !important;
  .border-outside {
    border-radius: 24px 24px 0px 17px !important;
  }
}
.tab_playDetail2::v-deep .tab-btn:nth-child(1) {
  border-right: 7.23px solid #fdf5ed !important;
  .border-outside {
    border-radius: 24px 24px 0px 0px !important;
  }
}
.tab_playDetail2::v-deep .tab-btn:nth-child(2) {
  border-right: 7.23px solid #fdf5ed !important;
  .border-outside {
    border-radius: 24px 24px 17px 0px !important;
  }
}
.tab_playDetail2::v-deep .tab-body {
  // background: linear-gradient(180deg, #ffe1c3 -7.11%, #ffffff 88.66%);

  border: 2px solid transparent !important;
  background-clip: padding-box, border-box !important;
  background-origin: padding-box, border-box !important;
  background-image: -webkit-gradient(
      linear,
      left top,
      left bottom,
      color-stop(-7.11%, #ffe1c3),
      color-stop(88.66%, #ffffff)
    ),
    -webkit-gradient(linear, left top, left bottom, color-stop(10%, #ffb74a), color-stop(15%, #fff)) !important;
  background-image: linear-gradient(180deg, #ffe1c3 -7.11%, #ffffff 88.66%),
    linear-gradient(180deg, #ffb74a 10%, #fff 90%) !important;
}

.stockDisabled {
  border-radius: 60px;
  height: 46px;
  box-shadow: 0px 0px 20px 0px rgba(255, 255, 255, 0.6) inset;
  color: #fff;
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
  letter-spacing: 0.64px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0px 48px;
  margin-left: 12px;
  position: relative;
  border: solid 1px transparent;
  background: #969696;
  background-clip: padding-box, border-box;

  background-origin: padding-box, border-box;

  background-image: linear-gradient(to right, #969696),
    linear-gradient(90deg, #ffddbe 5%, #ffc085 97.55%) !important;
  // background: linear-gradient(94.41deg, #ffddbe 5%, #ffc085 97.55%);
  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   right: 0;
  //   bottom: 0;
  //   z-index: 0;
  //   margin: 1px;
  //   background: #969696;
  //   position: absolute;
  //   border-radius: 60px;
  // }
  div {
    position: relative;
    z-index: 1;
  }
}
.tableValue {
  // height: 50px;
  padding: 3px 0px !important;
  color: #1b1b1b;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;

  line-height: 24px;
  letter-spacing: 0.56px;
  // padding: 0px !important;
}
.box_one {
  margin-top: 5.24px;
  width: 1120px;
}
.goWxBox {
  width: 1200px;
  min-width: 500px;
  max-width: 1200px;
  height: 70px;
  background: #fff;
  position: fixed;
  bottom: 0px;
  left: 50%;
  transition: transform 1s ease;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1001;
  padding: 0px 80px;
  border-radius: 20px;
  box-shadow: 0 0 0.625rem rgba(0, 0, 0, 0.06);

  .right-btn-text {
    font-size: 16px;
    color: #ff720c;
    font-family: PingFang Sc;
    cursor: pointer;
  }
  .goWxBoxClone {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #969696;
    border-radius: 50%;
    top: -14px;
    right: -14px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
}
.goWxBoxContent {
  font-size: 16px;
  font-family: PingFang Sc;
  color: #1b1b1b;

  .goWxBoxContent-text {
    color: #9a9a9a;
    font-size: 14px;
  }
}
.wechatDialog_box {
  text-align: center;
  margin-top: 35.137px;
  padding-bottom: 22.282px;
  // padding-bottom: 24px;
  // border-bottom: 1px solid #ffeed5;
  .title {
    color: #ff720c;
    font-family: YouSheBiaoTiHei;
    font-size: 26px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    margin-bottom: 10.28px;
  }
  .qRcode {
    width: 109px;
    height: 109px;
    border: 1px solid #ffb74a;
  }
  .divider {
    width: 385.65px;
    height: 1px;
    background: #ffeed5;
    margin: 0 auto;
    margin-top: 24px;
  }
  .footer_title {
    margin-top: 14.5px;
    color: #1b1b1b;
    font-family: YouSheBiaoTiHei;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 32px; /* 190% */
  }
  .footer_type {
    height: 18px;
    color: #969696;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    //
    .divide_right {
      display: block;
      height: 11px;
      margin: 0 10px;
      width: 1px;
      background: rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
