<template>
  <div
    v-loading.fullscreen.lock="listLoading"
    ref="bodyScroll"
    class="dark_container scrollPageSmoth"
    element-loading-text="拼命加载中"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
  >
    <headerKk />

    <div class="paTopCom">
      <div class="safe_width">
        <!-- 面包屑导航 -->
        <el-breadcrumb
          separator-class="el-icon-arrow-right"
          class="pdTopBottom my-bread"
          style="padding: 16px 0 20px 0px"
        >
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item class="el-breadcrumb__inner_text"
            >商品列表</el-breadcrumb-item
          >
        </el-breadcrumb>

        <!-- 页面标题 -->
        <div class="page-title">
          <h1>精选商品</h1>
          <p>为您精心挑选的优质商品</p>
        </div>

        <!-- 商品网格 -->
        <div class="products-grid">
          <div
            v-for="(item, index) in productList"
            :key="index"
            class="product-card"
            @click="goToDetail(item)"
          >
            <!-- 商品图片 -->
            <div class="product-image">
              <el-image
                :src="item.pic || defaultImage"
                style="width: 100%; height: 100%"
                fit="cover"
              ></el-image>
              <!-- 预览按钮 -->
              <div class="preview-overlay" @click.stop="showImagePreview(item)">
                预览
              </div>
              <!-- 已售出标识 -->
              <el-image
                v-if="item.stock == 0 || item.stock == 1"
                class="sold-overlay"
                style="width: 100%; height: 100%"
                src="../../../static/soled.jpg"
                fit="cover"
              ></el-image>
              <!-- 标签 -->
              <div
                v-if="item.tagsKKList && item.tagsKKList.length"
                class="product-tag"
              >
                {{ item.tagsKKList[0] }}
              </div>
            </div>

            <!-- 商品信息 -->
            <div class="product-info">
              <!-- 商品标题 -->
              <div class="product-title">
                {{ item.name || item.subTitle }}
              </div>

              <!-- 商品描述 -->
              <div class="product-desc text_linTwo">
                {{ formatProductDesc(item) }}
              </div>

              <!-- 游戏信息 -->
              <div class="product-game-info">
                <span class="game-name">{{ item.productCategoryName }}</span>
                <span class="game-server">{{ item.gameAccountQufu }}</span>
              </div>

              <!-- 价格和统计信息 -->
              <div class="product-bottom">
                <div class="product-stats">
                  <div class="stat-item">
                    <IconFont :size="14" icon="hot" color="#FF720C" />
                    <span>{{ item.gameSysinfoReadcount || 0 }}</span>
                  </div>
                  <div class="stat-item">
                    <IconFont :size="13" icon="focus" color="#FF720C" />
                    <span>{{ item.gameSysinfoCollectcount || 0 }}</span>
                  </div>
                </div>
                <div class="product-price">¥{{ item.price }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="totalPage > 0" class="pagination-wrapper">
          <el-pagination
            :current-page="currentPage"
            :page-size="pageSize"
            :total="totalPage"
            layout="prev, pager, next"
            class="custom-pagination"
            @current-change="handlePageChange"
          >
          </el-pagination>
        </div>

        <!-- 空状态 -->
        <div
          v-if="!listLoading && productList.length === 0"
          class="empty-state"
        >
          <img src="../../../static/imgs/null.png" alt="暂无数据" />
          <p>暂无商品数据</p>
        </div>
      </div>
    </div>

    <safeNews />
    <div class="footerCt">
      <footerKk />
    </div>

    <!-- 图片预览组件 -->
    <swperImagePriview
      v-if="showViewer"
      :table-data="tableData"
      :product-sn="productSn"
      :z-index="10000"
      :initial-index="imgViewer"
      :on-close="closeViewer"
      :url-list="arrDtPicForShow"
      :table-data-flag="true"
      :product="productObj"
      :game-sysinfo-readcount="gameSysinfoReadcount"
      :game-sysinfo-collectcount="gameSysinfoCollectcount"
      :price="price"
    />
  </div>
</template>

<script>
import headerKk from '@/components/headerKk/index';
import footerKk from '@/components/footerKk/index';
import safeNews from '@/components/safeNews/index';
import swperImagePriview from '@/components/imagePriview.vue';
import { searchProductList2 } from '@/api/search.js';
import { getDetailByCode } from '@/api/playDetail';
import { mapState } from 'vuex';
import util from '@/utils/index';
import defaultImage from '../../../static/imgs/sl_img.png';

export default {
  name: 'StoreList',
  components: {
    headerKk,
    footerKk,
    safeNews,
    swperImagePriview,
  },
  data() {
    return {
      listLoading: false,
      productList: [],
      currentPage: 1,
      pageSize: 12,
      totalPage: 0,
      defaultImage,

      // 图片预览相关
      showViewer: false,
      arrDtPicForShow: [],
      imgViewer: 0,
      productSn: '',
      gameSysinfoReadcount: '',
      gameSysinfoCollectcount: '',
      price: '',
      productObj: {},
      tableData: [],
    };
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo,
    }),
  },
  mounted() {
    this.fetchProductList();
  },
  methods: {
    // 获取商品列表
    fetchProductList() {
      this.listLoading = true;

      const searchParam = {
        pageNum: this.currentPage,
        pageSize: this.pageSize,
      };

      if (this.userInfo.id) {
        searchParam.memberId = this.userInfo.id;
      }

      const data = {};

      searchProductList2(searchParam, data)
        .then((response) => {
          this.listLoading = false;
          if (response.code == 200) {
            this.productList = response.data.list || [];
            this.totalPage = response.data.total || 0;

            // 处理商品数据
            this.productList.forEach((item) => {
              // 处理特殊属性
              this.processProductAttributes(item);
            });
          }
        })
        .catch(() => {
          this.listLoading = false;
        });
    },

    // 处理商品属性
    processProductAttributes(item) {
      const findtss = item.attrValueList?.find(
        (attr) => attr.name === '已使用天赏石'
      );
      const findtss2 = item.attrValueList?.find(
        (attr) => attr.name === '未使用天赏石'
      );
      const findtss3 = item.attrValueList?.find(
        (attr) => attr.name === '账号专区'
      );
      const findtss4 = item.attrValueList?.find(
        (attr) => attr.name === '账号类型'
      );
      const findtss5 = item.attrValueList?.find((attr) => attr.name === '职业');

      item.tssnum = 0;
      item.topAccount = '';
      item.accountType = '';
      item.careers = '';

      if (findtss) {
        item.tssnum = findtss.intValue;
      }
      if (findtss2) {
        item.tssnum = item.tssnum + findtss2.intValue;
      }
      if (findtss3) {
        item.topAccount = findtss3.value;
      }
      if (findtss4) {
        item.accountType = findtss4.value;
      }
      if (findtss5) {
        item.careers = findtss5.value;
      }
    },

    // 格式化商品描述
    formatProductDesc(item) {
      return util.tedianFilter(item.subTitle, item);
    },

    // 分页处理
    handlePageChange(page) {
      this.currentPage = page;
      this.fetchProductList();
      this.scrollToTop();
    },

    // 滚动到顶部
    scrollToTop() {
      let scrollEl = this.$refs.bodyScroll;
      scrollEl.scrollTo({
        top: 0,
        behavior: 'smooth',
      });
    },

    // 跳转到商品详情
    goToDetail(item) {
      let routeUrl = this.$router.resolve({
        path: `/gd/${item.productSn}`,
      });
      window.open(routeUrl.href, '_blank');
    },

    // 显示图片预览
    showImagePreview(item) {
      getDetailByCode({ productSn: item.productSn }).then((res) => {
        const product = res.data.product;
        let albumPicsJson = product.albumPicsJson ? product.albumPicsJson : [];
        let arr = [];

        if (product.albumPics) {
          arr = product.albumPics
            .split(',')
            .filter((item) => item.trim() !== '');
        } else {
          albumPicsJson = JSON.parse(albumPicsJson);
          albumPicsJson.forEach((item) => {
            if (arr.length < 10 && item.url) {
              arr.push(item.url);
            }
          });
        }

        this.productObj = item;
        this.productSn = item.productSn;
        this.gameSysinfoReadcount = item.gameSysinfoReadcount;
        this.gameSysinfoCollectcount = item.gameSysinfoCollectcount;
        this.price = item.price;

        let oldArr = this.mergeOptions(
          res.data.productAttributeList,
          res.data.productAttributeValueList
        );
        let newArr = [];
        let newArr2 = [];

        oldArr.forEach((item) => {
          if (item.selectType == 2) {
            newArr2.push(item);
          } else {
            newArr.push(item);
          }
        });

        newArr.sort((a, b) => b.type - a.type);
        newArr2.sort((a, b) => b.sort - a.sort);

        if (res.data.product.description) {
          newArr2.push({
            name: `【卖家说】${res.data.product.description}`,
            value: '',
            sort: 11,
            selectType: 2,
          });
        }

        let allArr = newArr.concat(newArr2);
        this.tableData = allArr;

        arr.unshift(defaultImage);
        this.arrDtPicForShow = arr;
        this.showViewer = true;
      });
    },

    // 合并选项
    mergeOptions(productAttributeList, productAttributeValueList) {
      productAttributeList.sort((a, b) => a.type - b.type);
      productAttributeList.sort((a, b) => b.sort - a.sort);

      let tempList = [];
      productAttributeList.forEach((ele) => {
        if (ele.type === 1 || ele.type === 2) {
          const findV = productAttributeValueList.find((item) => {
            return item.productAttributeId === ele.id;
          });
          if (findV && findV.value) {
            tempList.push({
              name:
                ele.selectType == 2
                  ? `【${ele.name}】${this.formatValue(findV.value)}`
                  : ele.name,
              label: ele.name,
              value: this.formatValue(findV.value),
              sort: ele.sort,
              selectType: ele.selectType,
            });
          }
        }
      });
      return tempList;
    },

    // 格式化值
    formatValue(value) {
      return value
        .replace(/[,]/g, '，')
        .replace(/\[核\]/g, '')
        .replace(/\[绝\]/g, '')
        .replace(/\[钱\]/g, '');
    },

    // 关闭预览
    closeViewer() {
      this.showViewer = false;
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.paTopCom {
  padding-top: 20px;
  padding-bottom: 80px;
}

.page-title {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-family: 'YouSheBiaoTiHei';
    font-size: 32px;
    font-weight: 400;
    line-height: 38px;
    color: #222222;
    margin: 0 0 12px 0;
  }

  p {
    font-size: 16px;
    color: #969696;
    margin: 0;
  }
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.product-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);

    .preview-overlay {
      display: flex;
    }
  }
}

.product-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;

  .preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: none;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    z-index: 2;
  }

  .sold-overlay {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3;
  }

  .product-tag {
    position: absolute;
    top: 12px;
    left: 12px;
    background: linear-gradient(135deg, #ff720c, #ffb74a);
    color: #fff;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    z-index: 4;
  }
}

.product-info {
  padding: 16px;
}

.product-title {
  font-size: 16px;
  font-weight: 600;
  color: #222222;
  line-height: 22px;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-desc {
  font-size: 14px;
  color: #666666;
  line-height: 20px;
  margin-bottom: 12px;
  height: 40px;
}

.product-game-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;

  .game-name {
    color: #ffb74a;
    font-weight: 500;
  }

  .game-server {
    color: #969696;
    margin-left: 8px;

    &::before {
      content: '|';
      margin-right: 8px;
      color: #e0e0e0;
    }
  }
}

.product-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-stats {
  display: flex;
  gap: 16px;

  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: #969696;
  }
}

.product-price {
  font-size: 20px;
  font-weight: 600;
  color: #ff720c;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.custom-pagination {
  ::v-deep .el-pager li {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin: 0 4px;

    &.active {
      background: linear-gradient(135deg, #ff720c, #ffb74a);
      border-color: #ff720c;
      color: #fff;
    }

    &:hover {
      border-color: #ff720c;
      color: #ff720c;
    }
  }

  ::v-deep .btn-prev,
  ::v-deep .btn-next {
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;

    &:hover {
      border-color: #ff720c;
      color: #ff720c;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 80px 0;

  img {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  p {
    font-size: 16px;
    color: #969696;
    margin: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 16px;
  }

  .product-card {
    border-radius: 12px;
  }

  .product-image {
    height: 160px;
  }

  .product-info {
    padding: 12px;
  }

  .page-title h1 {
    font-size: 24px;
  }
}
</style>